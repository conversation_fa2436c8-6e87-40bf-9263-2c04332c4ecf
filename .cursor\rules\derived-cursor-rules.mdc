---
description: AI rules derived by <PERSON><PERSON><PERSON><PERSON> from the project AI interaction history
globs: *
---

## <headers/>

## CODE QUALITY & STYLE

- Follow PEP 8 guidelines for Python code.
- Use descriptive variable and function names.
- Write comments to explain complex logic.
- Limit line length to 120 characters.
- All code must be modular and reusable.
- Avoid code duplication by using functions and classes.
- Standardize to top-level imports for clarity and improve static analysis. Organize imports alphabetically and by sections.
- Favor logging over `print()` statements, but `print()` statements are acceptable for quick debugging in scripts.

## TECH STACK

- Python 3.10+
- Solana Python SDK
- Jupiter API
- Pydantic (ensure compatibility with `pattern` instead of `regex` for field validation)
- `base58`
- Any other libraries specified in `requirements.txt` or `requirements_advanced.txt`.

## PROJECT DOCUMENTATION & CONTEXT SYSTEM

- Project specifications are in `docs/ProjectSpec.md`.
- Architectural decisions are documented in `docs/Architecture.md`.
- Step-by-step guides are in `docs/Step-by-Step-Guide.md`.
- Production deployment guides are in `docs/Production-Deployment-Guide.md`.
- Advanced setup guides are in `docs/Advanced-Setup-Guide.md`.
- All significant changes must be recorded in `docs/CHANGELOG.md`.

## WORKFLOW & RELEASE RULES

- Use feature branches for all new development.
- All code must be reviewed before merging to the main branch.
- Use semantic versioning for releases.
- Create pull requests for all code changes.
- Ensure all tests pass before merging.

## TESTING

- Write unit tests for all core functionality.
- Use pytest for testing.
- Aim for 100% test coverage.
- Integration tests should cover interactions between modules.

## DEBUGGING

- Use logging for debugging, not `print()` statements (although `print()` statements are acceptable for quick debugging in scripts).
- Log levels should be used appropriately (DEBUG, INFO, WARNING, ERROR, CRITICAL).
- Include sufficient information in logs to diagnose issues.
- When using `except` blocks, always catch specific exceptions rather than using bare `except:` to avoid masking unexpected errors.

## DEPENDENCY MANAGEMENT

- Use `pip` for managing dependencies.
- Specify dependencies in `requirements.txt` and `requirements_advanced.txt`.
- Keep dependencies up to date.
- Use virtual environments to isolate project dependencies.

## SECURITY

- Sanitize all user inputs to prevent injection attacks.
- Store sensitive data securely (e.g., using environment variables or encrypted files).
- Regularly audit code for security vulnerabilities.
- Follow secure coding practices.

## ERROR HANDLING

- Use try-except blocks to handle potential exceptions.
- Log all exceptions.
- Provide informative error messages to the user.
- Implement retry mechanisms for transient errors.

## IMPORT STATEMENTS

- **Critical:** Avoid using `sys.path.append()` to modify the Python import path, except within runner scripts as a temporary workaround. This practice *generally* leads to inconsistent and hard-to-maintain code.
- Within runner scripts: Add the project root to the Python path at the beginning of the file using:
```python
import sys
import os

# Add project root to Python path for imports
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
```
- Use relative imports (e.g., `from .module import ...`) for intra-package references within the `src` package.
- Use absolute imports (e.g., `from src.module import ...`) for cross-package references.
- Organize imports alphabetically within each section (standard library, third-party, local).
- Ensure runners import from `src.<module>` only.