## Overview

This project is a Solana memecoin sniper bot written in Python. It's designed for micro-capital trading and includes features like real-time discovery of new tokens on Pump.fun, automated rug checks, and trade execution through Jupiter. The bot is designed to be run in a dry-run mode for testing and has several safety features to prevent financial loss.

## Project Structure

The project is structured into several directories:

- `src/`: Contains the core logic of the bot.
  - `config.py`: Manages configuration.
  - `solana_wallet.py`: Handles wallet creation and management.
  - `rpc.py`: Manages the connection to the Solana RPC.
  - `jupiter_client.py`: Interacts with the Jupiter API for swaps.
  - `storage.py`: Handles database operations for tracking trades.
  - `pumpfun_watcher.py`: Monitors Pump.fun for new tokens.
  - `rug_checks.py`: Performs safety checks on new tokens.
  - `engine.py`: The main orchestrator of the bot.
- `runners/`: Contains scripts for running the bot.
  - `dryrun_solana.py`: Runs the bot in a safe, simulated environment.
- `docs/`: Contains project documentation.

## Developer Workflows

### Running the Bot

**Always start with dry-run mode to test the system safely!**

1.  **Set up the environment:**
    -   Create a virtual environment and activate it.
    -   Install the dependencies from `requirements.txt`.
    -   Create a `.env` file from the `.env.example` template and configure it with your wallet's secret key and RPC endpoints.

2.  **Run in dry-run mode:**
    ```bash
    python runners/dryrun_solana.py
    ```
    This will connect to the Solana network, monitor for new tokens, and run rug checks without executing any real trades.

3.  **Running in live mode:**
    -   **Use with extreme caution!**
    -   Change the `MODE` in your `.env` file to `live`.
    -   Run the bot with `python main.py`.

### Testing

You can test individual components of the bot by running the corresponding scripts in the `src/` directory. For example, to test the wallet connection, you can run:

```bash
python src/solana_wallet.py
```

## Conventions and Patterns

-   The bot is designed to be highly modular, with each component responsible for a specific task.
-   Configuration is managed through a central `config.py` file and a `.env` file.
-   The bot uses a SQLite database to store trade history and performance metrics.
-   Safety is a key consideration, with features like circuit breakers, position limits, and automatic stop-loss/take-profit.

## Integration Points

-   **Solana:** The bot interacts with the Solana blockchain through a Solana RPC.
-   **Pump.fun:** The bot monitors Pump.fun for new token launches via WebSocket.
-   **Jupiter:** The bot uses the Jupiter API for optimal swap execution.
-   **Birdeye:** The bot can optionally use the Birdedeye API for enhanced token analysis.
