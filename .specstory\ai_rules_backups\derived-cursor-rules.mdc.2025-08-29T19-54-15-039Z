
## <headers/>

## CODE QUALITY & STYLE

- Follow PEP 8 guidelines for Python code.
- Use descriptive variable and function names.
- Write comments to explain complex logic.
- Limit line length to 120 characters.
- All code must be modular and reusable.
- Avoid code duplication by using functions and classes.

## TECH STACK

- Python 3.10+
- Solana Python SDK
- Jupiter API
- Any other libraries specified in `requirements.txt` or `requirements_advanced.txt`.

## PROJECT DOCUMENTATION & CONTEXT SYSTEM

- Project specifications are in `docs/ProjectSpec.md`.
- Architectural decisions are documented in `docs/Architecture.md`.
- Step-by-step guides are in `docs/Step-by-Step-Guide.md`.
- Production deployment guides are in `docs/Production-Deployment-Guide.md`.
- Advanced setup guides are in `docs/Advanced-Setup-Guide.md`.
- All significant changes must be recorded in `docs/CHANGELOG.md`.

## WORKFLOW & RELEASE RULES

- Use feature branches for all new development.
- All code must be reviewed before merging to the main branch.
- Use semantic versioning for releases.
- <PERSON>reate pull requests for all code changes.
- Ensure all tests pass before merging.

## TESTING

- Write unit tests for all core functionality.
- Use pytest for testing.
- Aim for 100% test coverage.
- Integration tests should cover interactions between modules.

## DEBUGGING

- Use logging for debugging, not `print()` statements.
- Log levels should be used appropriately (DEBUG, INFO, WARNING, ERROR, CRITICAL).
- Include sufficient information in logs to diagnose issues.

## DEPENDENCY MANAGEMENT

- Use `pip` for managing dependencies.
- Specify dependencies in `requirements.txt` and `requirements_advanced.txt`.
- Keep dependencies up to date.
- Use virtual environments to isolate project dependencies.

## SECURITY

- Sanitize all user inputs to prevent injection attacks.
- Store sensitive data securely (e.g., using environment variables or encrypted files).
- Regularly audit code for security vulnerabilities.
- Follow secure coding practices.

## ERROR HANDLING

- Use try-except blocks to handle potential exceptions.
- Log all exceptions.
- Provide informative error messages to the user.
- Implement retry mechanisms for transient errors.

## IMPORT STATEMENTS

- **Critical:** Avoid using `sys.path.append()` to modify the Python import path. This practice leads to inconsistent and hard-to-maintain code.
- Use relative imports (e.g., `from .module import ...`) for intra-package references.
- Use absolute imports (e.g., `from src.module import ...`) for cross-package references.
- Organize imports alphabetically within each section (standard library, third-party, local).