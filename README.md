# Solana Memecoin Sniper Bot

**⚠️ EDUCATIONAL USE ONLY - NOT FINANCIAL ADVICE ⚠️**

A sophisticated Python trading bot designed for micro-capital memecoin trading on Solana, featuring real-time Pump.fun discovery, comprehensive rug checks, Jupiter swap execution, and advanced risk management.

## 🚀 Features

- **Real-time Discovery**: Monitor Pump.fun for new token launches via WebSocket
- **Advanced Rug Checks**: Automated screening for burned authorities, liquidity, and holder concentration
- **Jupiter Integration**: Optimal swap execution with price impact validation
- **Micro-Capital Optimized**: Designed for trading with as little as $5 initial capital
- **Comprehensive Safety**: Circuit breakers, position limits, and automatic stop-loss/take-profit
- **Performance Tracking**: Detailed trade history and analytics with SQLite storage
- **Dry-Run Mode**: Safe testing environment with simulated execution

## 📋 Requirements

- **Python 3.10+** (required for modern async/await patterns)
- **Solana Wallet** with SOL for trading
- **RPC Access** to Solana mainnet (recommended: QuickNode, Alchemy, or Helius)
- **Optional**: Birdeye API key for enhanced token analysis

## 🛠️ Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd solana-memecoin-sniper-bot
```

### 2. Set Up Python Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Linux/Mac:
source venv/bin/activate
# On Windows:
venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Create Solana Wallet (if needed)

```bash
# Install Solana CLI (if not already installed)
sh -c "$(curl -sSfL https://release.solana.com/v1.16.0/install)"

# Create new wallet
solana-keygen new --outfile /tmp/bot-wallet.json --no-passphrase

# Get the secret key array for configuration
cat /tmp/bot-wallet.json

# Fund the wallet with SOL for trading
# Copy the public key and send SOL from another wallet or exchange
```

### 4. Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit configuration (use your preferred editor)
nano .env
```

**Critical Configuration Steps:**

1. **Set your wallet secret key**:
   ```bash
   SOL_SECRET_KEY_JSON=[1,2,3,...64 integers from your wallet file]
   ```

2. **Configure RPC endpoints** (recommended to use a paid service):
   ```bash
   SOL_RPC_URL=https://your-rpc-endpoint.com
   SOL_WS_URL=wss://your-websocket-endpoint.com
   ```

3. **Adjust trading parameters** for your risk tolerance:
   ```bash
   TRADE_NOTIONAL_SOL=0.02      # ~$5 at $250/SOL
   MAX_SLIPPAGE_BPS=50          # 0.5% max slippage
   MAX_PRICE_IMPACT_PCT=1.0     # 1.0% max price impact
   ```

4. **Optional API keys**:
   ```bash
   BIRDEYE_API_KEY=your_birdeye_key_here
   QUICKNODE_ENDPOINT=your_quicknode_endpoint
   ```

## 🧪 Testing & Dry-Run

**ALWAYS start with dry-run mode to test the system safely!**

### 1. Verify Configuration

```python
python -c "
from src.config import get_config
config = get_config()
print('✅ Configuration loaded successfully!')
print(f'Mode: {config.system.mode}')
print(f'Trade size: {config.trading.trade_notional_sol} SOL')
"
```

### 2. Test Individual Components

```python
# Test wallet connection
python src.solana_wallet.py

# Test RPC connection
python src.rpc.py

# Test Jupiter integration
python src.jupiter_client.py

# Test database
python src.storage.py
```

### 3. Run Dry-Run Mode

```python
# Create the dry-run runner
python runners/dryrun_solana.py
```

This will:
- Connect to Solana network
- Monitor for new tokens (without executing trades)
- Run rug checks and log results
- Show what trades would be executed
- Track performance metrics

## 📊 Monitoring & Analytics

### Real-Time Monitoring

The bot provides structured logging with different levels:

```bash
# View live logs
tail -f bot.log

# Filter for specific events
grep "Token discovered" bot.log
grep "Trade executed" bot.log
grep "CIRCUIT BREAKER" bot.log
```

### Database Analysis

```python
# Check recent trades
from src.storage import get_database
db = get_database()

recent_trades = db.get_recent_trades(limit=10)
for trade in recent_trades:
    print(f"{trade.timestamp}: {trade.side} {trade.symbol} - {trade.amount_sol:.4f} SOL")

# Get performance metrics
perf = db.calculate_performance_metrics(days=7)
print(f"Win rate: {perf.win_rate_pct:.1f}%")
print(f"Total PnL: {perf.total_pnl_sol:.6f} SOL")
```

## ⚠️ Going Live (Use with Extreme Caution)

**Only proceed to live trading after extensive testing in dry-run mode!**

### Pre-Live Checklist

- [ ] Dry-run mode works correctly for at least 24 hours
- [ ] All rug checks are functioning properly
- [ ] Database is recording trades and performance
- [ ] You understand the risks and potential losses
- [ ] You have only allocated funds you can afford to lose
- [ ] Circuit breaker settings are appropriate

### Enable Live Trading

1. **Change mode in .env**:
   ```bash
   MODE=live
   ```

2. **Start with minimal size**:
   ```bash
   TRADE_NOTIONAL_SOL=0.01  # Start very small
   ```

3. **Run the bot**:
   ```python
   python main.py
   ```

4. **The system will require explicit confirmation**:
   ```
   ⚠️  WARNING: LIVE TRADING MODE ENABLED ⚠️
   This will use real funds and execute real trades!
   Trade size: 0.01 SOL
   Max slippage: 50 bps
   Max price impact: 1.0%
   
   Type 'I UNDERSTAND THE RISKS' to continue: 
   ```

## 🛡️ Safety Features

### Automatic Protections

- **Rug Checks**: Automatic screening for burned authorities, adequate liquidity, reasonable holder distribution
- **Price Impact Limits**: Reject trades with excessive price impact
- **Slippage Controls**: Maximum allowed slippage to prevent MEV attacks  
- **Circuit Breakers**: Automatic trading halt on consecutive failures
- **Position Limits**: Maximum position size as percentage of portfolio
- **Time Limits**: Automatic position closure after maximum hold time

### Manual Controls

- **Emergency Stop**: Press Ctrl+C to safely shut down
- **Mode Switching**: Change between dry-run and live in configuration
- **Parameter Adjustment**: Modify risk limits without code changes

## 📁 Project Structure

```
solana-memecoin-sniper-bot/
├── src/                          # Core modules
│   ├── config.py                 # Configuration management
│   ├── logutil.py               # Logging utilities
│   ├── solana_wallet.py         # Wallet management
│   ├── rpc.py                   # RPC/WebSocket client
│   ├── jupiter_client.py        # Jupiter API integration
│   ├── storage.py               # Database operations
│   ├── pumpfun_watcher.py       # Pump.fun event monitoring
│   ├── rug_checks.py            # Safety checks
│   └── engine.py                # Main orchestrator
├── runners/                     # Execution scripts
│   └── dryrun_solana.py         # Dry-run testing
├── docs/                        # Documentation
│   ├── ProjectSpec.md           # Project specifications
│   ├── Architecture.md          # Technical architecture
│   └── References.md            # External references
├── requirements.txt             # Python dependencies
├── .env.example                 # Configuration template
└── README.md                    # This file
```

## 🔧 Configuration Reference

### Trading Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| `TRADE_NOTIONAL_SOL` | 0.02 | SOL amount per trade (~$5) |
| `MAX_SLIPPAGE_BPS` | 50 | Maximum slippage (0.5%) |
| `MAX_PRICE_IMPACT_PCT` | 1.0 | Maximum price impact (1.0%) |
| `MIN_LIQUIDITY_SOL` | 5.0 | Minimum required liquidity |
| `MAX_TOP10_HOLDER_PCT` | 60 | Max top holder concentration |

### Safety Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| `TP_PCT` | 5.0 | Take profit percentage |
| `SL_PCT` | 3.0 | Stop loss percentage |
| `MAX_HOLD_MINUTES` | 10 | Maximum position hold time |
| `MAX_CONSECUTIVE_FAILURES` | 3 | Circuit breaker trigger |

### System Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| `MODE` | dryrun | Operation mode (dryrun/live) |
| `LOG_LEVEL` | INFO | Logging verbosity |
| `PUMPFUN_PROGRAM_ID` | 6EF8r... | Pump.fun program ID |

## 🐛 Troubleshooting

### Common Issues

**1. Configuration Errors**
```bash
# Verify configuration
python -c "from src.config import get_config; get_config()"
```

**2. RPC Connection Issues**
```bash
# Test RPC connectivity
python src.rpc.py
```

**3. Wallet Issues**
```bash
# Check wallet balance
python -c "
from src.config import get_config
from src.solana_wallet import create_wallet_from_config
config = get_config()
wallet = create_wallet_from_config(config)
balance = wallet.get_balance()
print(f'Wallet balance: {balance:.6f} SOL')
"
```

**4. Database Issues**
```bash
# Reset database (WARNING: destroys all data)
rm trading_bot.db
python src.storage.py
```

### Performance Issues

**1. Slow Discovery**: Consider using a faster RPC provider (QuickNode, Alchemy)

**2. High Latency**: Ensure good network connection and geographically close RPC

**3. Memory Usage**: Monitor with `htop` or Task Manager, restart if memory leaks

### Getting Help

1. **Check Logs**: Always review logs first
2. **Test Components**: Use individual module tests
3. **Verify Configuration**: Ensure all settings are correct
4. **Network Issues**: Test RPC endpoints separately

## ⚖️ Legal & Risk Disclaimers

### Educational Purpose
This software is provided for **educational and research purposes only**. It is designed to teach concepts related to:
- Automated trading systems
- Solana blockchain development  
- Risk management in volatile markets
- Software architecture for financial applications

### Not Financial Advice
This software does **NOT** constitute:
- Investment advice
- Financial recommendations
- Trading signals
- Guaranteed profit strategies

### Risk Warnings
Cryptocurrency trading carries **significant risks**:
- **Total Loss of Capital**: You may lose all invested funds
- **High Volatility**: Prices can change rapidly and unpredictably
- **Technical Risks**: Software bugs, network issues, and API failures can cause losses
- **Market Risks**: Rug pulls, liquidity drains, and market manipulation are common
- **Regulatory Risks**: Laws may change affecting the legality of trading activities

### User Responsibilities
By using this software, you acknowledge:
- You are responsible for compliance with all applicable laws
- You understand the risks and trade only with funds you can afford to lose
- You will test thoroughly in dry-run mode before any live trading
- You are solely responsible for any profits or losses
- The developers are not liable for any damages or losses

### No Warranties
This software is provided "as is" without warranties of any kind, including:
- Merchantability or fitness for a particular purpose
- Accuracy or completeness
- Error-free operation
- Compatibility with all systems

## 📜 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

This is an educational project. Contributions should focus on:
- Educational value and code clarity
- Safety improvements and risk management
- Documentation and testing
- Bug fixes and stability improvements

Please ensure all contributions maintain the educational focus and include appropriate risk warnings.

---

**Remember: This is educational software. Never risk more than you can afford to lose. Always thoroughly test in dry-run mode first.**