#!/usr/bin/env python3
"""
create-wallet-devnet.py

1) Generate a new Solana keypair
2) Save secret key JSON to disk
3) Connect to Devnet
4) Request 5 SOL airdrop
5) Verify Devnet balance
"""

import json
import asyncio
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from solana.rpc.async_api import AsyncClient

AIR_DROP_SOL = 5  # Devnet only

async def main():
    # 1) Create a new keypair
    wallet = Keypair()
    pubkey: Pubkey = wallet.pubkey()
    pubkey_str = str(pubkey)

    print("---")
    print("✨ New Devnet wallet created successfully! ✨")
    print("---")
    print(f"🔑 Public Key (Address): {pubkey_str}")
    print("🤫 Secret Key: [REDACTED FOR SECURITY]")
    print("---")
    print("⚠️  NOTE: The secret key is not displayed here for security reasons.")
    print("    It is saved in the JSON file below.")
    print("---")

    # 2) Save secret key JSON
    file_path = f"wallet-{pubkey_str}.json"
    with open(file_path, "w") as f:
        f.write(wallet.to_json())
    print(f"✅ Wall<PERSON>'s secret key saved to: {file_path}\n")

    # 3) Connect to Devnet
    client = AsyncClient("https://api.devnet.solana.com")
    print("🔗 Connected to Devnet RPC: https://api.devnet.solana.com")

    # 4) Airdrop 5 SOL (lamports = SOL * 1e9)
    lamports = AIR_DROP_SOL * 1_000_000_000
    print(f"💧 Requesting {AIR_DROP_SOL} SOL airdrop...")
    airdrop_resp = await client.request_airdrop(pubkey, lamports)
    # Newer solana-py returns .value (Signature) on success
    sig = getattr(airdrop_resp, "value", None) or airdrop_resp.get("result")
    print(f"   Airdrop signature: {sig}")

    # 5) Poll balance until the airdrop lands
    print("⏳ Waiting for airdrop confirmation...")
    balance_sol = 0.0
    for _ in range(20):  # up to ~20s
        bal_resp = await client.get_balance(pubkey)
        # Newer solana-py returns .value; fallback to dict if needed
        value = getattr(bal_resp, "value", None)
        if value is None:
            value = bal_resp.get("result", {}).get("value", 0)
        balance_sol = value / 1_000_000_000
        if balance_sol >= AIR_DROP_SOL:
            break
        await asyncio.sleep(1.0)

    await client.close()
    print(f"✅ Devnet balance for {pubkey_str}: {balance_sol:.4f} SOL")
    print("\n💡 This funded Devnet wallet is ready for full paper-trading on-chain.")

if __name__ == "__main__":
    asyncio.run(main())
