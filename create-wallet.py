from solders.keypair import Keypair
import json

# 1. Create a new Keypair
# This generates a new, random wallet
wallet = Keypair()

# 2. Display wallet details
# The public key is your wallet address. You can share it with others.
# The secret key is private and should never be shared.
print("---")
print("✨ New wallet created successfully! ✨")
print("---")
print(f"🔑 Public Key (Address): {wallet.pubkey()}")
print(f"🤫 Secret Key: [REDACTED FOR SECURITY]")
print("---")
print("⚠️  NOTE: The secret key is not displayed here for security reasons.")
print("    It is saved in the JSON file below.")
print("---")


# 3. Save the secret key to a file
# The secret key is needed to sign transactions and access your funds.
# Keep this file safe and do not share it with anyone.
file_path = f"wallet-{wallet.pubkey()}.json"
with open(file_path, "w") as f:
    f.write(wallet.to_json())

print(f"✅ Wallet's secret key saved to: {file_path}")
print("\n💡 You can now use this file to load your wallet in other scripts.")


# 4. Example of how to load the wallet
# You can use this code in your other scripts to load the wallet
try:
    with open(file_path, 'r') as f:
        loaded_wallet_data = json.load(f)
        loaded_secret_key = bytes(loaded_wallet_data)
        loaded_keypair = Keypair.from_bytes(loaded_secret_key)

    print("\n---")
    print("✅ Successfully loaded wallet from file:")
    print(f"   - Public Key: {loaded_keypair.pubkey()}")
    # We don't print the secret key for security
    print(f"   - Secret Key: [LOADED SUCCESSFULLY]")
    print("---")

except Exception as e:
    print(f"\n❌ Error loading wallet: {e}")