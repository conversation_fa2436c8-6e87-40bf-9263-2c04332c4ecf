# Solana Memecoin Sniper Bot - Project Specification

## Project Overview

This project develops a comprehensive Python trading bot system specifically designed for micro-capital memecoin trading on Solana, with initial capital as small as $5. The bot integrates advanced rug checks, Jupiter swap execution, and real-time Pump.fun discovery to identify and execute trades on emerging tokens while maintaining strict risk controls.

## Objectives

### Primary Goals
1. **Micro-Capital Optimization**: Enable profitable trading with initial capital as small as $5 by leveraging Solana's low fees and strict slippage controls
2. **Automated Discovery**: Real-time monitoring of Pump.fun token launches and bonding curve events  
3. **Advanced Safety**: Comprehensive rug check system including burned authorities, liquidity thresholds, and holder concentration analysis
4. **High-Performance Execution**: Sub-second trade execution via Jupiter aggregator with strict price impact controls
5. **Risk Management**: Circuit breakers, position limits, and automated stop-loss/take-profit systems

### Secondary Goals
1. **Educational Framework**: Clear, documented architecture for learning automated trading concepts
2. **Extensible Design**: Modular structure allowing easy strategy modifications and new platform integration
3. **Comprehensive Logging**: Detailed telemetry for performance analysis and strategy refinement

## Scope & Requirements

### In Scope
- **Solana Blockchain Only**: Focus exclusively on Solana ecosystem tokens
- **Pump.fun Integration**: Primary discovery via Pump.fun program events (Program ID: `6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P`)
- **Jupiter Aggregator**: Primary execution via Jupiter v6 swap API for optimal routing
- **Micro-Capital Focus**: Optimization for trades between 0.01-0.1 SOL (~$2-20)
- **Dry-Run Default**: All testing and initial setup in simulated environment
- **Real-Time Processing**: WebSocket subscriptions for low-latency discovery

### Out of Scope
- **CEX Trading**: No centralized exchange integration
- **Multi-Chain**: Limited to Solana blockchain only
- **Complex Strategies**: Focus on simple, fast rotation strategies
- **Live Trading by Default**: Requires explicit opt-in with strong warnings

## Technical Requirements

### Core Dependencies
```
solana==0.30.2
anchorpy==0.18.0
httpx==0.27.0
websockets==12.0
pyNaCl==1.5.0
python-dotenv==1.0.1
pydantic==2.8.2
tenacity==8.3.0
pandas==2.2.2
numpy==2.0.1
sqlite3 (built-in)
```

### System Requirements
- **Python 3.10+**: For modern async/await patterns and type hints
- **Solana RPC Access**: Reliable WebSocket-enabled RPC endpoint
- **Network Latency**: <100ms to Solana mainnet for optimal execution
- **Memory**: Minimum 2GB RAM for in-memory data processing
- **Storage**: 1GB for trade logs and strategy backtesting data

## Architecture Overview

### Core Modules
1. **Configuration Layer** (`src/solana_config.py`, `.env.example`)
2. **Wallet Management** (`src/solana_wallet.py`) 
3. **RPC & WebSocket Client** (`src/rpc.py`)
4. **Jupiter Integration** (`src/jupiter_client.py`)
5. **Pump.fun Discovery** (`src/pumpfun_watcher.py`)
6. **Rug Check System** (`src/rug_checks.py`, `src/token_filters.py`)
7. **Strategy Engine** (`src/strategies/memecoin.py`)
8. **Orchestrator** (`src/engine.py`)
9. **Persistence** (`src/storage.py`)
10. **Dry-Run Framework** (`runners/dryrun_solana.py`)

## Risk Management Framework

### Critical Safeguards (Mandatory)
- **Authority Checks**: Verify burned mint and freeze authorities
- **Liquidity Thresholds**: Minimum liquidity requirements (default: 5 SOL)
- **Holder Concentration**: Maximum top-10 holder percentage (default: 60%)
- **Price Impact Limits**: Maximum impact per trade (default: 1.0%)
- **Slippage Controls**: Maximum allowed slippage (default: 50 bps)
- **Circuit Breakers**: Halt trading on consecutive failures

### Position Management
- **Size Limits**: Maximum position size as percentage of portfolio
- **Stop Loss**: Automated exit on adverse price movement (default: 3%)
- **Take Profit**: Automated exit on favorable price movement (default: 5%)
- **Time Limits**: Maximum holding period (default: 10 minutes)

## Configuration Parameters

### Environment Variables (.env)
```bash
# Solana Network
SOL_RPC_URL=https://api.mainnet-beta.solana.com
SOL_WS_URL=wss://api.mainnet-beta.solana.com
SOL_SECRET_KEY_JSON=[1,2,3,...] # Never commit

# Trading Parameters  
TRADE_NOTIONAL_SOL=0.02          # ~$5 at $250/SOL
MAX_SLIPPAGE_BPS=50              # 0.5%
MAX_PRICE_IMPACT_PCT=1.0         # 1.0%
MIN_LIQUIDITY_SOL=5.0            # Minimum pool liquidity
MAX_TOP10_HOLDER_PCT=60          # Holder concentration limit

# Safety Settings
REQUIRE_BURNED_MINT_AUTH=true    # Require burned authorities
MIN_MCAP_SOL=10.0               # Minimum market cap
MODE=dryrun                     # dryrun|live

# Optional APIs
BIRDEYE_API_KEY=                # For enhanced token analysis
QUICKNODE_ENDPOINT=             # For Metis Jupiter integration
```

## Strategy Specification

### Memecoin Micro-Trading Rules
1. **Entry Criteria**:
   - Pass all rug checks (authorities, liquidity, concentration)
   - Market cap between 10-1000 SOL
   - Price impact under 1.0% for planned trade size
   - Recent creation (under 1 hour old)

2. **Position Sizing**:
   - Fixed notional size (0.02 SOL default)
   - Never exceed 20% of total portfolio
   - Account for gas fees in sizing calculations

3. **Exit Criteria**:
   - Take profit at 5% gain (configurable)
   - Stop loss at 3% loss (configurable)
   - Time-based exit after 10 minutes
   - Liquidity drain detection

## Data Requirements

### Real-Time Feeds
- **Pump.fun Events**: New token creation via `logsSubscribe` 
- **Jupiter Quotes**: Price and route data for execution
- **Token Metadata**: Via Birdeye API or Solscan integration

### Historical Data
- **Trade Records**: All executed trades with timestamps
- **Performance Metrics**: Realized vs quoted slippage/impact
- **Strategy Analytics**: Win rate, average hold time, P&L distribution

## Testing & Validation

### Dry-Run Requirements
- **Simulated Execution**: Full trade lifecycle without real funds
- **Live Data Integration**: Real market data with paper trading
- **Performance Reporting**: Detailed metrics on hypothetical performance
- **Safety Testing**: Validate all rug checks and circuit breakers

### Acceptance Criteria
1. **Functional**: Complete dry-run execution with all safety checks
2. **Performance**: Sub-5-second discovery to quote latency
3. **Safety**: Zero bypass of mandatory rug checks
4. **Documentation**: Complete setup guide with troubleshooting
5. **Code Quality**: Full type hints, error handling, and logging

## Risks & Limitations

### Technical Risks
- **RPC Reliability**: Dependence on external Solana RPC providers
- **Network Congestion**: Solana network congestion affecting execution
- **API Changes**: Jupiter or Pump.fun API modifications breaking integration
- **Rate Limits**: API throttling affecting real-time performance

### Market Risks  
- **Rug Pulls**: Despite checks, sophisticated scams may bypass filters
- **Liquidity Risk**: Insufficient liquidity for exits on small positions
- **Slippage**: Actual execution prices varying from quotes
- **MEV**: Maximal Extractable Value affecting trade profitability

### Regulatory & Ethical Considerations
- **Educational Purpose**: This system is for research and education only
- **No Financial Advice**: Does not constitute investment recommendations  
- **User Responsibility**: Users responsible for compliance with local laws
- **Risk Disclosure**: Clear warnings about potential losses

## Success Metrics

### Technical KPIs
- **Discovery Latency**: <5 seconds from token creation to evaluation
- **Execution Success**: >95% of attempted trades successfully executed  
- **Safety Record**: Zero trades executed without passing all rug checks
- **Uptime**: >99% availability during market hours

### Trading Performance (Dry-Run)
- **Win Rate**: Target >60% of trades profitable
- **Average Return**: Target >2% per winning trade
- **Risk-Adjusted Return**: Positive Sharpe ratio in backtesting
- **Maximum Drawdown**: <10% of portfolio in dry-run scenarios

## Implementation Phases

### Phase 1: Foundation (Week 1)
- Basic configuration and wallet management
- Solana RPC client with WebSocket support
- Jupiter integration for quotes and swaps
- Initial dry-run framework

### Phase 2: Discovery & Safety (Week 2) 
- Pump.fun event subscription and parsing
- Comprehensive rug check implementation
- Token metadata integration (Birdeye/Solscan)
- Safety testing and validation

### Phase 3: Strategy & Orchestration (Week 3)
- Memecoin trading strategy implementation  
- Main orchestrator loop with all components
- Circuit breakers and position management
- Performance monitoring and logging

### Phase 4: Testing & Documentation (Week 4)
- Extensive dry-run testing and optimization
- Complete documentation and setup guides
- Performance analysis and reporting tools
- Final safety audits and user warnings

## Conclusion

This Solana Memecoin Sniper Bot represents a sophisticated yet accessible approach to automated memecoin trading, prioritizing safety and education while providing practical exposure to cutting-edge DeFi trading techniques. The modular architecture ensures maintainability and extensibility, while the comprehensive safety framework protects users from common pitfalls in volatile memecoin markets.

The focus on micro-capital trading makes this system accessible to a broad audience while teaching valuable lessons about risk management, automated trading, and the Solana ecosystem. All implementations prioritize transparency, safety, and educational value over pure profit optimization.