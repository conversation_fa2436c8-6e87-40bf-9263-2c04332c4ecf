# Solana Memecoin Sniper Bot - Complete Setup Walkthrough

**⚠️ EDUCATIONAL PURPOSE ONLY - NOT FINANCIAL ADVICE ⚠️**

This guide provides a comprehensive, step-by-step walkthrough for setting up and running the Solana Memecoin Sniper Bot, with detailed explanations of each component and how they work together.

## Table of Contents

1. [Prerequisites & Environment Setup](#prerequisites--environment-setup)
2. [Project Installation](#project-installation)  
3. [Configuration Deep Dive](#configuration-deep-dive)
4. [Component Testing](#component-testing)
5. [Dry-Run Execution](#dry-run-execution)
6. [Understanding the Results](#understanding-the-results)
7. [Advanced Configuration](#advanced-configuration)
8. [Safety Considerations](#safety-considerations)
9. [Troubleshooting Guide](#troubleshooting-guide)
10. [Next Steps](#next-steps)

## Prerequisites & Environment Setup

### System Requirements

**Why these requirements?**
- Python 3.10+ provides modern async/await syntax and improved type hints
- 4GB+ RAM ensures smooth operation with real-time data processing
- Stable internet connection is critical for low-latency trading operations

**Step 1: Verify Python Version**
```bash
python --version
# Should show Python 3.10.0 or higher
```

If you need to install Python 3.10+:
- **Windows**: Download from [python.org](https://www.python.org/downloads/)
- **macOS**: Use Homebrew: `brew install python@3.10`
- **Linux**: Use your package manager: `sudo apt install python3.10`

**Step 2: Install Solana CLI (Optional but Recommended)**
```bash
# Install Solana CLI
sh -c "$(curl -sSfL https://release.solana.com/v1.18.0/install)"

# Add to PATH
export PATH="/home/<USER>/.local/share/solana/install/active_release/bin:$PATH"

# Verify installation
solana --version
```

**Why Solana CLI?**
The CLI provides utilities for wallet creation, balance checking, and network interaction that complement our bot's functionality.

## Project Installation

### Step 1: Create Project Directory

```bash
# Create project directory
mkdir solana-memecoin-bot
cd solana-memecoin-bot

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Linux/macOS:
source venv/bin/activate
# On Windows:
venv\Scripts\activate
```

**Why virtual environments?**
Virtual environments isolate project dependencies, preventing conflicts with other Python projects and ensuring reproducible installations.

### Step 2: Install Dependencies

Create `requirements.txt`:
```txt
solana==0.30.2
anchorpy==0.18.0
httpx==0.27.0
websockets==12.0
pyNaCl==1.5.0
python-dotenv==1.0.1
pydantic==2.8.2
tenacity==8.3.0
pandas==2.2.2
numpy==2.0.1
```

Install dependencies:
```bash
pip install -r requirements.txt
```

**Dependency Explanations:**
- `solana`: Official Solana Python SDK for blockchain interactions
- `httpx`: Modern async HTTP client for API communications
- `websockets`: Real-time WebSocket connections for live data
- `pydantic`: Type-safe configuration and data validation
- `tenacity`: Robust retry logic for network operations

### Step 3: Create Project Structure

```bash
# Create directory structure
mkdir -p src runners docs

# You'll copy the provided modules into these directories
```

## Configuration Deep Dive

### Step 1: Create Wallet

**Option A: Use Solana CLI (Recommended)**
```bash
# Create new wallet (save the seed phrase!)
solana-keygen new --outfile wallet.json --no-passphrase

# Display public key
solana-keygen pubkey wallet.json

# Get secret key array for config
cat wallet.json
```

**Option B: Use Python Script**
```python
from solana.keypair import Keypair
import json

# Generate new keypair
keypair = Keypair.generate()

# Print public key
print(f"Public Key: {keypair.public_key}")

# Print secret key array for .env file
print(f"Secret Key Array: {list(keypair.secret_key)}")
```

**Why this format?**
The bot uses JSON array format for security and compatibility with Solana's standard keypair format.

### Step 2: Fund Wallet

```bash
# Get your public key
solana-keygen pubkey wallet.json

# Send SOL from another wallet or exchange
# Minimum recommended: 0.1 SOL for testing
```

**Funding Requirements:**
- **Testing**: 0.1 SOL minimum for comprehensive dry-run testing
- **Live Trading**: Start with 0.2-0.5 SOL for initial micro-capital trading

### Step 3: Configure Environment

Create `.env` file:
```bash
# Network Configuration
SOL_RPC_URL=https://api.mainnet-beta.solana.com
SOL_WS_URL=wss://api.mainnet-beta.solana.com

# Wallet (NEVER commit this!)
SOL_SECRET_KEY_JSON=[your,secret,key,array,here]

# Trading Parameters
TRADE_NOTIONAL_SOL=0.02
MAX_SLIPPAGE_BPS=50
MAX_PRICE_IMPACT_PCT=1.0
MIN_LIQUIDITY_SOL=5.0
MAX_TOP10_HOLDER_PCT=60
MIN_MCAP_SOL=10.0

# Safety Settings
REQUIRE_BURNED_MINT_AUTH=true
MODE=dryrun

# Strategy
TP_PCT=5.0
SL_PCT=3.0
MAX_HOLD_MINUTES=10

# Program IDs
PUMPFUN_PROGRAM_ID=6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P

# Logging
LOG_LEVEL=INFO
```

**Configuration Explanations:**

**Trading Parameters:**
- `TRADE_NOTIONAL_SOL=0.02`: Trade size (~$5 at $250/SOL)
- `MAX_SLIPPAGE_BPS=50`: Maximum 0.5% slippage tolerance
- `MAX_PRICE_IMPACT_PCT=1.0`: Reject trades with >1% price impact
- `MIN_LIQUIDITY_SOL=5.0`: Require minimum 5 SOL in liquidity pool

**Safety Settings:**
- `REQUIRE_BURNED_MINT_AUTH=true`: Only trade tokens with burned authorities
- `MODE=dryrun`: Always start in safe mode
- `TP_PCT=5.0`: Take profit at 5% gain
- `SL_PCT=3.0`: Stop loss at 3% loss

**Why these defaults?**
These parameters are optimized for micro-capital trading while maintaining strict risk controls. They can be adjusted based on your risk tolerance and testing results.

## Component Testing

### Step 1: Test Configuration Loading

```python
# test_config.py
from src.config import get_config

try:
    config = get_config()
    print("✅ Configuration loaded successfully!")
    print(f"Mode: {config.system.mode}")
    print(f"RPC URL: {config.solana.sol_rpc_url}")
    print(f"Trade size: {config.trading.trade_notional_sol} SOL")
    print(f"Max slippage: {config.trading.max_slippage_bps} bps")
except Exception as e:
    print(f"❌ Configuration error: {e}")
```

**What this tests:**
- Environment variable loading
- Pydantic validation
- Type safety and bounds checking

### Step 2: Test Wallet Connection

```python
# test_wallet.py
from src.config import get_config
from src.solana_wallet import create_wallet_from_config

try:
    config = get_config()
    wallet = create_wallet_from_config(config)
    print(f"✅ Wallet loaded: {wallet.public_key}")
    
    balance = wallet.get_balance()
    print(f"✅ Balance: {balance:.6f} SOL")
    
    if balance < 0.01:
        print("⚠️  Low balance - fund wallet for testing")
    
except Exception as e:
    print(f"❌ Wallet error: {e}")
```

**What this tests:**
- Keypair loading and validation
- RPC connectivity
- Balance retrieval

### Step 3: Test RPC Communication

```python
# test_rpc.py
import asyncio
from src.config import get_config
from src.rpc import create_rpc_client

async def test_rpc():
    try:
        config = get_config()
        
        async with create_rpc_client(config.solana.sol_rpc_url) as client:
            blockhash = await client.get_latest_blockhash()
            print(f"✅ RPC connected: {blockhash.get('blockhash', '')[:16]}...")
            
            # Test account info
            sol_mint = "So11111111111111111111111111111111111111112"
            account_info = await client.get_account_info(sol_mint)
            print(f"✅ Account info retrieved: {bool(account_info)}")
            
    except Exception as e:
        print(f"❌ RPC error: {e}")

asyncio.run(test_rpc())
```

**What this tests:**
- HTTP RPC connectivity
- Blockhash retrieval
- Account information queries

### Step 4: Test Jupiter Integration

```python
# test_jupiter.py
import asyncio
from src.jupiter_client import JupiterClient, SOL_MINT, sol_to_lamports

async def test_jupiter():
    try:
        usdc_mint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
        
        async with JupiterClient() as client:
            quote = await client.get_quote(
                input_mint=SOL_MINT,
                output_mint=usdc_mint,
                amount=sol_to_lamports(0.01),  # 0.01 SOL
                slippage_bps=50
            )
            
            print(f"✅ Jupiter quote received:")
            print(f"  Output: {quote.output_amount / 1_000_000:.2f} USDC")
            print(f"  Price impact: {quote.price_impact_pct:.3f}%")
            print(f"  Route: {quote.get_route_description()}")
            
    except Exception as e:
        print(f"❌ Jupiter error: {e}")

asyncio.run(test_jupiter())
```

**What this tests:**
- Jupiter API connectivity
- Quote request functionality
- Price impact calculation

### Step 5: Test Database Operations

```python
# test_database.py
from datetime import datetime, timezone
from src.storage import TradingDatabase, TokenRecord

try:
    db = TradingDatabase(":memory:")  # In-memory for testing
    
    # Test token insertion
    token = TokenRecord(
        mint="TestMint111111111111111111111111111111111",
        symbol="TEST",
        name="Test Token",
        first_seen=datetime.now(timezone.utc),
        creator="Creator111111111111111111111111111111111",
        source="test",
        metadata={"test": True}
    )
    
    success = db.insert_token(token)
    print(f"✅ Database insert: {'Success' if success else 'Failed'}")
    
    # Test performance calculation
    perf = db.calculate_performance_metrics(1)
    print(f"✅ Performance calculation: {'Success' if perf else 'Failed'}")
    
    db.close()
    
except Exception as e:
    print(f"❌ Database error: {e}")
```

**What this tests:**
- SQLite connectivity
- Table creation and indexing
- Data insertion and retrieval

## Dry-Run Execution

### Step 1: Verify All Components

Run all component tests to ensure everything works:
```bash
python test_config.py
python test_wallet.py
python test_rpc.py
python test_jupiter.py  
python test_database.py
```

All tests should pass before proceeding.

### Step 2: Run Dry-Run Bot

```bash
python runners/dryrun_solana.py
```

**What you should see:**
```
🧪 Solana Memecoin Sniper Bot - Dry Run Testing
============================================================
Mode: dryrun
Trade size: 0.02 SOL
RPC URL: https://api.mainnet-beta.solana.com

[2025-08-28 10:30:15] INFO main: 🚀 Solana Memecoin Sniper Bot starting up...
[2025-08-28 10:30:16] INFO wallet: Wallet initialized: 7X9...abc
[2025-08-28 10:30:17] INFO rpc: RPC client initialized: https://api.mainnet-beta.solana.com
[2025-08-28 10:30:18] INFO jupiter: Jupiter client initialized
[2025-08-28 10:30:19] INFO main: ✅ All components initialized successfully
[2025-08-28 10:30:20] INFO main: 🔄 Running cycle 1/2
[2025-08-28 10:30:21] INFO main: Starting discovery cycle...
[2025-08-28 10:30:22] INFO trading: Token discovered: TestToken1111... from pump.fun
```

**Step 3: Monitor the Output**

The dry-run will simulate:
1. **Token Discovery**: Finding new tokens on Pump.fun
2. **Rug Checks**: Safety validation for each token
3. **Quote Requests**: Getting Jupiter quotes for valid tokens
4. **Trade Simulation**: Simulating trade execution
5. **Performance Tracking**: Recording results in database

## Understanding the Results

### Log Analysis

**Token Discovery:**
```
[INFO] trading: Token discovered: TestToken1111... from pump.fun
[INFO] database: Token stored in database: TEST1 (TestToken1111...)
```
*Explanation*: Bot found a new token and stored it for processing.

**Rug Check Results:**
```
[INFO] safety: Authority check for TestToken1111...: Mint=BURNED, Freeze=BURNED
[INFO] safety: Liquidity check for TestToken1111...: 8.5 SOL (required: 5.0) - PASS
[INFO] safety: Holder concentration for TestToken1111...: 45.0% (max: 60.0%) - PASS
[INFO] trading: Rug check PASSED for TestToken1111...: authority, liquidity, concentration
```
*Explanation*: Token passed safety checks - authorities burned, adequate liquidity, good distribution.

**Trade Execution:**
```
[INFO] trading: Quote received: 1000 tokens (impact: 0.500%, slippage: 50bps)
[INFO] trading: Trade executed: BUY 0.02 of TestToken1111... at 0.000020 SOL (tx: simulated_tx_123...)
[INFO] trading: Position opened: 1000 of TEST1 at 0.000020 SOL (SL: 0.000019, TP: 0.000021)
```
*Explanation*: Bot got a good quote, executed the trade, and set stop-loss/take-profit levels.

### Database Analysis

Check the results in SQLite:
```python
from src.storage import get_database

db = get_database("dryrun_trading.db")

# View recent trades
trades = db.get_recent_trades(10)
for trade in trades:
    print(f"{trade.timestamp}: {trade.side} {trade.symbol} - {trade.amount_sol:.4f} SOL")

# Check performance
perf = db.calculate_performance_metrics(1)
print(f"Total trades: {perf.total_trades}")
print(f"Win rate: {perf.win_rate_pct:.1f}%")
print(f"Total PnL: {perf.total_pnl_sol:.6f} SOL")
```

### Performance Metrics

The bot tracks key performance indicators:
- **Win Rate**: Percentage of profitable trades
- **Average Return**: Mean profit per winning trade
- **Slippage Accuracy**: How well quotes match execution
- **Safety Effectiveness**: Percentage of tokens filtered by rug checks

## Advanced Configuration

### Fine-Tuning Parameters

Based on dry-run results, adjust configuration:

**If too many tokens are filtered:**
```bash
# Relax liquidity requirements
MIN_LIQUIDITY_SOL=3.0

# Allow higher concentration
MAX_TOP10_HOLDER_PCT=70
```

**If slippage is too high:**
```bash
# Tighten slippage tolerance
MAX_SLIPPAGE_BPS=30

# Reduce price impact limit
MAX_PRICE_IMPACT_PCT=0.5
```

**If trades are too small/large:**
```bash
# Adjust trade size
TRADE_NOTIONAL_SOL=0.01  # Smaller trades

# Modify profit targets
TP_PCT=3.0  # Lower profit target
SL_PCT=2.0  # Tighter stop loss
```

### RPC Optimization

For better performance, consider premium RPC providers:

**QuickNode:**
```bash
SOL_RPC_URL=https://your-endpoint.solana-mainnet.quiknode.pro/
SOL_WS_URL=wss://your-endpoint.solana-mainnet.quiknode.pro/
```

**Helius:**
```bash
SOL_RPC_URL=https://mainnet.helius-rpc.com/?api-key=your-key
SOL_WS_URL=wss://mainnet.helius-rpc.com/?api-key=your-key
```

**Why premium RPC?**
- Lower latency for faster execution
- Higher rate limits for more requests
- Better reliability and uptime

## Safety Considerations

### Pre-Live Checklist

Before considering live trading:

- [ ] Dry-run operates successfully for 24+ hours
- [ ] All safety checks function correctly
- [ ] Database records all trades properly
- [ ] You understand potential losses
- [ ] Only allocated risk capital
- [ ] Tested various market conditions
- [ ] Circuit breakers trigger appropriately

### Risk Management

**Position Sizing:**
Never risk more than you can afford to lose. Start with minimum amounts and increase gradually based on proven performance.

**Diversification:**
Don't put all capital into a single trade or token. The bot enforces position limits for this reason.

**Market Conditions:**
Memecoin markets are highly volatile and manipulated. Rug pulls and liquidity drains are common despite safety checks.

## Troubleshooting Guide

### Common Issues

**1. Configuration Errors**
```
❌ Configuration error: Field required
```
*Solution*: Check `.env` file for missing required fields.

**2. Wallet Connection Issues**
```
❌ Wallet error: Invalid secret key format
```
*Solution*: Verify secret key is valid JSON array of 64 integers.

**3. RPC Timeout**
```
❌ RPC error: Request timeout
```
*Solution*: Try different RPC endpoint or check network connectivity.

**4. Jupiter API Errors**
```
❌ Jupiter error: No routes found
```
*Solution*: Check if token pair is supported and liquidity exists.

**5. Database Locked**
```
❌ Database error: Database is locked
```
*Solution*: Ensure no other processes are accessing database file.

### Performance Issues

**Slow Discovery:**
- Use faster RPC provider
- Check network latency
- Optimize WebSocket connections

**High Memory Usage:**
- Monitor with `htop` or Task Manager
- Restart bot periodically
- Check for memory leaks

**API Rate Limits:**
- Use premium API endpoints
- Implement request queuing
- Monitor usage patterns

## Next Steps

### After Successful Dry-Run

1. **Extended Testing**: Run dry-run for several days across different market conditions
2. **Parameter Optimization**: Fine-tune based on performance data
3. **Strategy Refinement**: Adjust rug checks and trade criteria
4. **Real Integration**: Implement actual Pump.fun WebSocket listening
5. **Enhanced Analytics**: Add more sophisticated performance tracking

### Development Roadmap

**Phase 2: Real-Time Integration**
- Complete Pump.fun WebSocket implementation
- Real Birdeye API integration
- Live token metadata fetching

**Phase 3: Advanced Features**
- Multiple strategy support
- Machine learning token scoring  
- Portfolio optimization

**Phase 4: Production Ready**
- Multi-wallet support
- API for external integrations
- Advanced monitoring and alerting

### Educational Extensions

- Study Solana program interactions
- Learn about AMM mechanics and bonding curves
- Explore MEV protection techniques
- Research advanced trading strategies

## Conclusion

This walkthrough provides a complete foundation for understanding and operating the Solana Memecoin Sniper Bot. Remember:

**🎯 Educational Purpose**: This system teaches valuable concepts about automated trading, blockchain development, and risk management.

**⚠️ Risk Awareness**: Cryptocurrency trading carries significant risks. Never trade more than you can afford to lose.

**🔬 Scientific Approach**: Test thoroughly, measure results, and make data-driven decisions.

**📚 Continuous Learning**: The crypto space evolves rapidly. Keep learning and adapting your strategies.

Start with dry-run mode, understand every component, and only consider live trading after extensive testing and education. The goal is learning and skill development, not quick profits.

---

**Remember: This is educational software designed to teach trading concepts. It does not guarantee profits and all trading carries risk of loss.**