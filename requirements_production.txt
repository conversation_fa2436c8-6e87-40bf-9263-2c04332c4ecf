# Production-Ready Dependencies for Solana Trading Bot
# Updated for real transaction execution and DEX integration

# Core Solana and Web3 dependencies
solana>=0.34.2
solders>=0.21.0
construct
httpx>=0.27.2
websockets
pydantic>=2.0.0

# Real Solana operations
base58>=2.1.1
PyNaCl>=1.5.0

# Data processing and analysis
numpy>=1.25.0
pandas>=2.1.0
scipy>=1.11.0

# Machine learning and AI
scikit-learn>=1.3.0
tensorflow>=2.13.0  # For advanced AI models
torch>=2.0.0  # PyTorch for neural networks
transformers>=4.30.0  # For NLP and sentiment analysis

# Database and storage
sqlalchemy>=2.0.0
redis>=4.6.0  # For caching and real-time data
aiosqlite>=0.19.0

# Network and API clients
aiohttp>=3.8.0
requests>=2.31.0
websocket-client>=1.6.0

# Async and concurrency
aiofiles>=23.2.0
asyncpg>=0.28.0  # PostgreSQL async driver (optional)

# System monitoring
psutil>=5.9.0  # For system resource monitoring
py-cpuinfo>=9.0.0

# Cryptography and security
cryptography>=41.0.0
pynacl>=1.5.0
keyring>=24.2.0  # For secure key storage

# Configuration and environment
python-dotenv>=1.0.0
pydantic>=2.3.0
PyYAML>=6.0.1

# Logging and debugging
structlog>=23.1.0
rich>=13.5.0  # For beautiful console output
colorama>=0.4.6

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.0
coverage>=7.3.0

# Mathematical and statistical
statsmodels>=0.14.0
ta-lib>=0.4.26  # Technical analysis library
QuantLib>=1.32  # Quantitative finance

# Time and date handling
python-dateutil>=2.8.2
pytz>=2023.3

# Data validation and parsing
marshmallow>=3.20.0
cerberus>=1.3.5

# Image processing (for chart generation)
Pillow>=10.0.0
matplotlib>=3.7.0
plotly>=5.16.0

# Web scraping (for social sentiment)
beautifulsoup4>=4.12.0
selenium>=4.12.0
lxml>=4.9.3

# Natural language processing
nltk>=3.8.1
spacy>=3.6.1
textblob>=0.17.1

# Performance optimization
numba>=0.58.0  # JIT compilation for numerical functions
cython>=3.0.0

# Development tools
black>=23.7.0  # Code formatting
flake8>=6.0.0  # Linting
mypy>=1.5.0  # Type checking
pre-commit>=3.4.0

# Optional: GUI components (if building a desktop interface)
PyQt6>=6.5.0  # Alternative GUI framework

# Optional: Advanced features
jupyter>=1.0.0  # For data analysis notebooks
dash>=2.14.0  # For web dashboards
streamlit>=1.26.0  # Alternative dashboard framework

# Jito-specific dependencies
jito-py>=0.1.0  # Jito Python SDK (if available)

# Memory optimization
memory-profiler>=0.61.0
pympler>=0.9

# Parallel processing
joblib>=1.3.0

# Error handling and monitoring
sentry-sdk>=1.32.0  # Error tracking

# Additional utilities for production
typing-extensions>=4.0.0
dataclasses-json>=0.5.0
cachetools>=5.0.0
prometheus-client>=0.17.0  # Metrics collection

# Optional: Advanced analytics
apache-airflow>=2.7.0  # Workflow orchestration (for complex strategies)
prefect>=2.13.0  # Alternative workflow management

# Production deployment
gunicorn>=21.2.0  # WSGI server
uvicorn>=0.23.0  # ASGI server
fastapi>=0.103.0  # For API endpoints (if building REST API)

# Configuration management
hydra-core>=1.3.0  # Advanced configuration management
omegaconf>=2.3.0

# Caching and optimization
diskcache>=5.6.0
lru-dict>=1.2.0

# Social media APIs (for sentiment analysis)
tweepy>=4.14.0  # Twitter API
python-telegram-bot>=20.5.0  # Telegram bot API
discord.py>=2.3.0  # Discord bot API

# Financial data providers
yfinance>=0.2.18  # Yahoo Finance
alpha-vantage>=2.3.1  # Alpha Vantage API
ccxt>=4.1.0  # Cryptocurrency exchange library

# Load balancing and proxy
requests[socks]>=2.31.0
aiohttp[speedups]>=3.8.0

# Version pinning for stability (production recommendations)
# Uncomment and adjust versions based on your testing
# solana==0.34.2
# httpx==0.27.0
# numpy==1.25.2
# pandas==2.1.1