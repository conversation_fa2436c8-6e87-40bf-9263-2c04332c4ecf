"""
AI-Powered Token Scoring System

Advanced machine learning system for evaluating memecoin potential
using multi-factor analysis including on-chain metrics, social sentiment,
and creator behavior patterns.
"""
import asyncio
import json
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timezone, timedelta
from enum import Enum
import httpx
import pandas as pd



class RiskLevel(Enum):
    """Token risk levels"""
    VERY_LOW = "very_low"
    LOW = "low" 
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"

class TokenCategory(Enum):
    """Token categories for scoring"""
    MEMECOIN = "memecoin"
    UTILITY = "utility"
    GAMING = "gaming"
    DEFI = "defi"
    NFT = "nft"
    UNKNOWN = "unknown"

@dataclass
class SocialMetrics:
    """Social engagement metrics"""
    twitter_followers: int = 0
    twitter_engagement_rate: float = 0.0
    telegram_members: int = 0
    discord_members: int = 0
    reddit_subscribers: int = 0
    social_sentiment_score: float = 0.0  # -1 to 1
    mention_velocity: float = 0.0  # mentions per hour
    influencer_endorsements: int = 0

@dataclass
class OnChainMetrics:
    """On-chain token metrics"""
    market_cap_usd: float = 0.0
    liquidity_usd: float = 0.0
    volume_24h_usd: float = 0.0
    price_change_24h_pct: float = 0.0
    price_change_7d_pct: float = 0.0
    holder_count: int = 0
    top10_holder_pct: float = 0.0
    creator_balance_pct: float = 0.0
    burn_count: int = 0
    mint_authority_burned: bool = False
    freeze_authority_burned: bool = False
    trading_volume_trend: float = 0.0
    price_volatility: float = 0.0
    liquidity_stability: float = 0.0

@dataclass
class CreatorMetrics:
    """Token creator analysis"""
    previous_tokens_created: int = 0
    previous_success_rate: float = 0.0
    wallet_age_days: int = 0
    wallet_transaction_count: int = 0
    wallet_success_tokens: int = 0
    wallet_rug_pulls: int = 0
    social_presence_score: float = 0.0
    developer_activity_score: float = 0.0

@dataclass
class TechnicalMetrics:
    """Technical analysis metrics"""
    rsi_14: float = 50.0
    macd_signal: float = 0.0
    bollinger_position: float = 0.5  # 0-1 scale
    support_resistance_score: float = 0.0
    momentum_score: float = 0.0
    trend_strength: float = 0.0

@dataclass
class TokenScore:
    """Comprehensive token scoring result"""
    overall_score: float  # 0-100
    confidence: float    # 0-1
    risk_level: RiskLevel
    category: TokenCategory

    # Component scores
    social_score: float
    onchain_score: float
    creator_score: float
    technical_score: float

    # Factors and reasoning
    positive_factors: List[str]
    negative_factors: List[str]
    warning_flags: List[str]

    # Metadata
    scored_at: datetime
    model_version: str

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage"""
        data = asdict(self)
        data['scored_at'] = self.scored_at.isoformat()
        data['risk_level'] = self.risk_level.value
        data['category'] = self.category.value
        return data

class SocialAnalyzer:
    """Social media sentiment and engagement analyzer"""

    def __init__(self):
        self.http_client = httpx.AsyncClient(timeout=30.0)

        # Initialize logger
        from .logutil import get_logger
        self.logger = get_logger("social_analyzer")

    async def analyze_token_social(self, token_symbol: str, token_name: str) -> SocialMetrics:
        """Analyze social media metrics for a token"""
        try:
            metrics = SocialMetrics()

            # Twitter analysis (would integrate with Twitter API)
            twitter_data = await self._analyze_twitter(token_symbol, token_name)
            metrics.twitter_followers = twitter_data.get('followers', 0)
            metrics.twitter_engagement_rate = twitter_data.get('engagement_rate', 0.0)

            # Telegram analysis (would integrate with Telegram APIs)
            telegram_data = await self._analyze_telegram(token_symbol)
            metrics.telegram_members = telegram_data.get('members', 0)

            # Sentiment analysis
            sentiment_score = await self._analyze_sentiment(token_symbol, token_name)
            metrics.social_sentiment_score = sentiment_score

            # Mention velocity
            mention_velocity = await self._calculate_mention_velocity(token_symbol)
            metrics.mention_velocity = mention_velocity

            self.logger.debug(f"Social analysis completed for {token_symbol}")
            return metrics

        except Exception as e:
            self.logger.warning(f"Social analysis failed for {token_symbol}: {e}")
            return SocialMetrics()

    async def _analyze_twitter(self, symbol: str, name: str) -> Dict[str, Any]:
        """Analyze Twitter metrics (placeholder - would need Twitter API)"""
        # In production, this would use Twitter API v2
        # For now, return simulated data based on symbol patterns

        score_multipliers = {
            'PEPE': 2.0, 'DOGE': 3.0, 'SHIB': 2.5, 'BONK': 1.8, 'WIF': 1.5
        }

        base_followers = 1000
        multiplier = score_multipliers.get(symbol.upper(), 1.0)

        return {
            'followers': int(base_followers * multiplier),
            'engagement_rate': min(0.15, multiplier * 0.05)  # Cap at 15%
        }

    async def _analyze_telegram(self, symbol: str) -> Dict[str, Any]:
        """Analyze Telegram metrics (placeholder)"""
        # Would integrate with Telegram Bot API
        return {'members': 500}

    async def _analyze_sentiment(self, symbol: str, name: str) -> float:
        """Analyze social sentiment (simplified implementation)"""
        # In production, would use NLP models for sentiment analysis
        positive_keywords = ['moon', 'pump', 'bullish', 'gem', 'diamond', 'hold']
        negative_keywords = ['dump', 'rug', 'scam', 'sell', 'bearish', 'dead']

        # Simplified sentiment based on name/symbol
        name_lower = name.lower()
        symbol_lower = symbol.lower()

        positive_score = sum(1 for word in positive_keywords if word in name_lower)
        negative_score = sum(1 for word in negative_keywords if word in name_lower)

        if positive_score + negative_score == 0:
            return 0.0

        sentiment = (positive_score - negative_score) / (positive_score + negative_score)
        return max(-1.0, min(1.0, sentiment))

    async def _calculate_mention_velocity(self, symbol: str) -> float:
        """Calculate mention velocity (mentions per hour)"""
        # Would track mentions across platforms
        # Simplified implementation
        return 10.0  # Default mentions per hour

class OnChainAnalyzer:
    """On-chain metrics analyzer"""

    def __init__(self):
        self.http_client = httpx.AsyncClient(timeout=30.0)

        # Initialize logger  
        from .logutil import get_logger
        self.logger = get_logger("onchain_analyzer")

    async def analyze_token_onchain(self, mint: str) -> OnChainMetrics:
        """Analyze on-chain metrics for a token"""
        try:
            metrics = OnChainMetrics()

            # Get basic token info
            token_info = await self._get_token_info(mint)

            # Authority checks
            metrics.mint_authority_burned = token_info.get('mint_authority_burned', False)
            metrics.freeze_authority_burned = token_info.get('freeze_authority_burned', False)

            # Market data (would integrate with price APIs)
            market_data = await self._get_market_data(mint)
            metrics.market_cap_usd = market_data.get('market_cap', 0.0)
            metrics.liquidity_usd = market_data.get('liquidity', 0.0)
            metrics.volume_24h_usd = market_data.get('volume_24h', 0.0)
            metrics.price_change_24h_pct = market_data.get('price_change_24h', 0.0)

            # Holder analysis
            holder_data = await self._analyze_holders(mint)
            metrics.holder_count = holder_data.get('total_holders', 0)
            metrics.top10_holder_pct = holder_data.get('top10_percentage', 0.0)
            metrics.creator_balance_pct = holder_data.get('creator_percentage', 0.0)

            # Technical indicators
            metrics.trading_volume_trend = await self._calculate_volume_trend(mint)
            metrics.price_volatility = await self._calculate_volatility(mint)
            metrics.liquidity_stability = await self._calculate_liquidity_stability(mint)

            self.logger.debug(f"On-chain analysis completed for {mint}")
            return metrics

        except Exception as e:
            self.logger.warning(f"On-chain analysis failed for {mint}: {e}")
            return OnChainMetrics()

    async def _get_token_info(self, mint: str) -> Dict[str, Any]:
        """Get basic token information"""
        # Would query Solana RPC for token account info
        return {
            'mint_authority_burned': True,
            'freeze_authority_burned': True,
            'supply': **********
        }

    async def _get_market_data(self, mint: str) -> Dict[str, Any]:
        """Get market data from price APIs"""
        # Would integrate with DexScreener, Birdeye, etc.
        return {
            'market_cap': 100000.0,
            'liquidity': 50000.0,
            'volume_24h': 25000.0,
            'price_change_24h': 5.0
        }

    async def _analyze_holders(self, mint: str) -> Dict[str, Any]:
        """Analyze token holder distribution"""
        # Would analyze largest token holders
        return {
            'total_holders': 1500,
            'top10_percentage': 45.0,
            'creator_percentage': 5.0
        }

    async def _calculate_volume_trend(self, mint: str) -> float:
        """Calculate trading volume trend"""
        # Would analyze volume over time
        return 1.2  # 20% increase in volume trend

    async def _calculate_volatility(self, mint: str) -> float:
        """Calculate price volatility"""
        # Would calculate statistical volatility
        return 0.8  # High volatility typical for memecoins

    async def _calculate_liquidity_stability(self, mint: str) -> float:
        """Calculate liquidity stability score"""
        # Would track liquidity changes over time
        return 0.7  # Moderately stable liquidity

class CreatorAnalyzer:
    """Token creator behavior analyzer"""

    def __init__(self):
        self.http_client = httpx.AsyncClient(timeout=30.0)

        # Initialize logger
        from .logutil import get_logger
        self.logger = get_logger("creator_analyzer")

    async def analyze_creator(self, creator_address: str) -> CreatorMetrics:
        """Analyze token creator history and behavior"""
        try:
            metrics = CreatorMetrics()

            # Wallet age and activity
            wallet_data = await self._get_wallet_data(creator_address)
            metrics.wallet_age_days = wallet_data.get('age_days', 0)
            metrics.wallet_transaction_count = wallet_data.get('transaction_count', 0)

            # Previous token creation history
            token_history = await self._get_creator_token_history(creator_address)
            metrics.previous_tokens_created = len(token_history)

            # Success rate calculation
            successful_tokens = [t for t in token_history if t.get('success', False)]
            if token_history:
                metrics.previous_success_rate = len(successful_tokens) / len(token_history)
                metrics.wallet_success_tokens = len(successful_tokens)

            # Rug pull detection
            rug_pulls = [t for t in token_history if t.get('rug_pull', False)]
            metrics.wallet_rug_pulls = len(rug_pulls)

            # Social presence scoring
            metrics.social_presence_score = await self._score_social_presence(creator_address)

            # Developer activity
            metrics.developer_activity_score = await self._score_developer_activity(creator_address)

            self.logger.debug(f"Creator analysis completed for {creator_address}")
            return metrics

        except Exception as e:
            self.logger.warning(f"Creator analysis failed for {creator_address}: {e}")
            return CreatorMetrics()

    async def _get_wallet_data(self, address: str) -> Dict[str, Any]:
        """Get wallet age and basic data"""
        # Would query Solana RPC and analysis services
        return {
            'age_days': 90,
            'transaction_count': 150
        }

    async def _get_creator_token_history(self, creator_address: str) -> List[Dict[str, Any]]:
        """Get history of tokens created by this address"""
        # Would query token creation history
        return [
            {'mint': 'token1...', 'success': True, 'rug_pull': False},
            {'mint': 'token2...', 'success': False, 'rug_pull': True},
        ]

    async def _score_social_presence(self, creator_address: str) -> float:
        """Score creator's social media presence"""
        # Would check for verified social accounts linked to wallet
        return 0.6

    async def _score_developer_activity(self, creator_address: str) -> float:
        """Score creator's development activity"""
        # Would check GitHub, smart contract deployments, etc.
        return 0.4

class TechnicalAnalyzer:
    """Technical analysis for tokens"""

    def __init__(self):
        # Initialize logger
        from .logutil import get_logger
        self.logger = get_logger("technical_analyzer")

    async def analyze_technical(self, mint: str) -> TechnicalMetrics:
        """Perform technical analysis on token"""
        try:
            metrics = TechnicalMetrics()

            # Get price history
            price_data = await self._get_price_history(mint)

            if not price_data:
                return metrics

            prices = np.array([p['close'] for p in price_data])

            # RSI calculation
            metrics.rsi_14 = self._calculate_rsi(prices, 14)

            # MACD
            metrics.macd_signal = self._calculate_macd_signal(prices)

            # Bollinger Bands position
            metrics.bollinger_position = self._calculate_bollinger_position(prices)

            # Support/Resistance
            metrics.support_resistance_score = self._calculate_support_resistance(prices)

            # Momentum
            metrics.momentum_score = self._calculate_momentum(prices)

            # Trend strength
            metrics.trend_strength = self._calculate_trend_strength(prices)

            self.logger.debug(f"Technical analysis completed for {mint}")
            return metrics

        except Exception as e:
            self.logger.warning(f"Technical analysis failed for {mint}: {e}")
            return TechnicalMetrics()

    async def _get_price_history(self, mint: str, periods: int = 100) -> List[Dict[str, float]]:
        """Get historical price data"""
        # Would integrate with price data providers
        # Return simulated OHLC data
        import random
        base_price = 0.001

        data = []
        for i in range(periods):
            change = random.uniform(-0.1, 0.1)
            base_price *= (1 + change)
            data.append({
                'open': base_price,
                'high': base_price * 1.05,
                'low': base_price * 0.95,
                'close': base_price,
                'volume': random.uniform(1000, 10000)
            })

        return data

    def _calculate_rsi(self, prices: np.ndarray, period: int = 14) -> float:
        """Calculate RSI indicator"""
        if len(prices) < period + 1:
            return 50.0

        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])

        if avg_loss == 0:
            return 100.0

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))

        return float(rsi)

    def _calculate_macd_signal(self, prices: np.ndarray) -> float:
        """Calculate MACD signal line"""
        if len(prices) < 26:
            return 0.0

        # Simplified MACD calculation
        ema12 = self._ema(prices, 12)
        ema26 = self._ema(prices, 26)
        macd_line = ema12[-1] - ema26[-1]

        return float(macd_line)

    def _ema(self, prices: np.ndarray, period: int) -> np.ndarray:
        """Calculate exponential moving average"""
        alpha = 2.0 / (period + 1)
        ema = np.zeros_like(prices)
        ema[0] = prices[0]

        for i in range(1, len(prices)):
            ema[i] = alpha * prices[i] + (1 - alpha) * ema[i-1]

        return ema

    def _calculate_bollinger_position(self, prices: np.ndarray) -> float:
        """Calculate position within Bollinger Bands (0-1)"""
        if len(prices) < 20:
            return 0.5

        sma = np.mean(prices[-20:])
        std = np.std(prices[-20:])

        upper_band = sma + (2 * std)
        lower_band = sma - (2 * std)

        current_price = prices[-1]

        if upper_band == lower_band:
            return 0.5

        position = (current_price - lower_band) / (upper_band - lower_band)
        return max(0.0, min(1.0, position))

    def _calculate_support_resistance(self, prices: np.ndarray) -> float:
        """Calculate support/resistance strength score"""
        # Simplified implementation
        current_price = prices[-1]
        recent_high = np.max(prices[-20:])
        recent_low = np.min(prices[-20:])

        # Score based on position relative to recent range
        range_position = (current_price - recent_low) / (recent_high - recent_low) if recent_high != recent_low else 0.5

        # Higher score if near support/resistance levels
        if range_position < 0.2 or range_position > 0.8:
            return 0.8
        else:
            return 0.3

    def _calculate_momentum(self, prices: np.ndarray) -> float:
        """Calculate momentum score"""
        if len(prices) < 10:
            return 0.0

        # Rate of change over 10 periods
        roc = (prices[-1] / prices[-10] - 1) * 100

        # Normalize to -1 to 1 scale
        momentum = max(-1.0, min(1.0, roc / 50))  # Assuming 50% is extreme
        return momentum

    def _calculate_trend_strength(self, prices: np.ndarray) -> float:
        """Calculate trend strength"""
        if len(prices) < 20:
            return 0.0

        # Linear regression slope
        x = np.arange(len(prices[-20:]))
        y = prices[-20:]

        if len(x) > 1:
            slope = np.polyfit(x, y, 1)[0]
            # Normalize slope relative to average price
            avg_price = np.mean(y)
            if avg_price > 0:
                trend_strength = slope / avg_price * 20  # Scale factor
                return max(-1.0, min(1.0, trend_strength))

        return 0.0

class AITokenScorer:
    """
    Advanced AI-powered token scoring system

    Combines multiple analysis layers to provide comprehensive
    token evaluation with confidence scoring and risk assessment.
    """

    def __init__(self):
        self.social_analyzer = SocialAnalyzer()
        self.onchain_analyzer = OnChainAnalyzer()
        self.creator_analyzer = CreatorAnalyzer()
        self.technical_analyzer = TechnicalAnalyzer()

        # Scoring weights (can be optimized through ML)
        self.weights = {
            'social': 0.25,
            'onchain': 0.35, 
            'creator': 0.25,
            'technical': 0.15
        }

        # Model version
        self.model_version = "v1.0.0"

        # Initialize logger
        from .logutil import get_logger
        self.logger = get_logger("ai_scorer")

    async def score_token(self, mint: str, symbol: str, name: str, 
                         creator_address: str) -> TokenScore:
        """
        Comprehensive token scoring using all analysis components

        Args:
            mint: Token mint address
            symbol: Token symbol
            name: Token name  
            creator_address: Creator wallet address

        Returns:
            TokenScore with overall score and detailed analysis
        """
        try:
            self.logger.info(f"Starting AI token scoring for {symbol} ({mint})")

            # Run all analyses in parallel
            analyses = await asyncio.gather(
                self.social_analyzer.analyze_token_social(symbol, name),
                self.onchain_analyzer.analyze_token_onchain(mint),
                self.creator_analyzer.analyze_creator(creator_address),
                self.technical_analyzer.analyze_technical(mint),
                return_exceptions=True
            )

            social_metrics, onchain_metrics, creator_metrics, technical_metrics = analyses

            # Handle any analysis failures
            if isinstance(social_metrics, Exception):
                self.logger.warning(f"Social analysis failed: {social_metrics}")
                social_metrics = SocialMetrics()

            if isinstance(onchain_metrics, Exception):
                self.logger.warning(f"On-chain analysis failed: {onchain_metrics}")
                onchain_metrics = OnChainMetrics()

            if isinstance(creator_metrics, Exception):
                self.logger.warning(f"Creator analysis failed: {creator_metrics}")
                creator_metrics = CreatorMetrics()

            if isinstance(technical_metrics, Exception):
                self.logger.warning(f"Technical analysis failed: {technical_metrics}")
                technical_metrics = TechnicalMetrics()

            # Calculate component scores
            social_score = self._score_social_metrics(social_metrics)
            onchain_score = self._score_onchain_metrics(onchain_metrics)
            creator_score = self._score_creator_metrics(creator_metrics)
            technical_score = self._score_technical_metrics(technical_metrics)

            # Calculate weighted overall score
            overall_score = (
                social_score * self.weights['social'] +
                onchain_score * self.weights['onchain'] +
                creator_score * self.weights['creator'] +
                technical_score * self.weights['technical']
            )

            # Calculate confidence based on data quality
            confidence = self._calculate_confidence(
                social_metrics, onchain_metrics, creator_metrics, technical_metrics
            )

            # Determine risk level
            risk_level = self._determine_risk_level(
                overall_score, onchain_metrics, creator_metrics
            )

            # Categorize token
            category = self._categorize_token(symbol, name)

            # Identify factors
            positive_factors, negative_factors, warning_flags = self._identify_factors(
                social_metrics, onchain_metrics, creator_metrics, technical_metrics
            )

            token_score = TokenScore(
                overall_score=overall_score,
                confidence=confidence,
                risk_level=risk_level,
                category=category,
                social_score=social_score,
                onchain_score=onchain_score,
                creator_score=creator_score,
                technical_score=technical_score,
                positive_factors=positive_factors,
                negative_factors=negative_factors,
                warning_flags=warning_flags,
                scored_at=datetime.now(timezone.utc),
                model_version=self.model_version
            )

            self.logger.info(
                f"Token scoring completed for {symbol}: "
                f"Score={overall_score:.1f}, Risk={risk_level.value}, "
                f"Confidence={confidence:.2f}"
            )

            return token_score

        except Exception as e:
            self.logger.error(f"Token scoring failed for {symbol}: {e}")

            # Return minimum viable score on failure
            return TokenScore(
                overall_score=0.0,
                confidence=0.0,
                risk_level=RiskLevel.VERY_HIGH,
                category=TokenCategory.UNKNOWN,
                social_score=0.0,
                onchain_score=0.0,
                creator_score=0.0,
                technical_score=0.0,
                positive_factors=[],
                negative_factors=["Analysis failed"],
                warning_flags=["Scoring system error"],
                scored_at=datetime.now(timezone.utc),
                model_version=self.model_version
            )

    def _score_social_metrics(self, metrics: SocialMetrics) -> float:
        """Score social media metrics (0-100)"""
        score = 0.0

        # Twitter following and engagement
        if metrics.twitter_followers > 0:
            follower_score = min(30, metrics.twitter_followers / 1000 * 10)  # Max 30 points
            engagement_score = metrics.twitter_engagement_rate * 100  # Max based on engagement
            score += follower_score + min(20, engagement_score)

        # Sentiment analysis
        sentiment_score = (metrics.social_sentiment_score + 1) * 25  # Convert -1,1 to 0,50
        score += sentiment_score

        # Mention velocity
        if metrics.mention_velocity > 0:
            velocity_score = min(15, metrics.mention_velocity * 1.5)  # Max 15 points
            score += velocity_score

        # Community size
        total_community = (metrics.telegram_members + metrics.discord_members + 
                          metrics.reddit_subscribers)
        if total_community > 0:
            community_score = min(10, total_community / 500)  # Max 10 points
            score += community_score

        return min(100.0, max(0.0, score))

    def _score_onchain_metrics(self, metrics: OnChainMetrics) -> float:
        """Score on-chain metrics (0-100)"""
        score = 0.0

        # Authority checks (critical)
        if metrics.mint_authority_burned and metrics.freeze_authority_burned:
            score += 25
        elif metrics.mint_authority_burned or metrics.freeze_authority_burned:
            score += 10
        else:
            return 0.0  # Fail immediately if authorities not burned

        # Liquidity and market cap
        if metrics.liquidity_usd > 0:
            liquidity_score = min(20, np.log10(metrics.liquidity_usd) * 5)
            score += liquidity_score

        if metrics.market_cap_usd > 0:
            mcap_score = min(15, np.log10(metrics.market_cap_usd) * 3)
            score += mcap_score

        # Holder distribution
        if metrics.top10_holder_pct <= 50:
            holder_score = 20 * (1 - metrics.top10_holder_pct / 100)
            score += holder_score

        # Trading activity
        if metrics.volume_24h_usd > 0 and metrics.liquidity_usd > 0:
            volume_ratio = metrics.volume_24h_usd / metrics.liquidity_usd
            volume_score = min(10, volume_ratio * 5)  # Max 10 points
            score += volume_score

        # Price performance
        if abs(metrics.price_change_24h_pct) < 50:  # Not too volatile
            stability_score = 10 - abs(metrics.price_change_24h_pct) / 10
            score += max(0, stability_score)

        return min(100.0, max(0.0, score))

    def _score_creator_metrics(self, metrics: CreatorMetrics) -> float:
        """Score creator behavior (0-100)"""
        score = 50.0  # Start neutral

        # Previous success rate
        if metrics.previous_tokens_created > 0:
            success_bonus = metrics.previous_success_rate * 30
            score += success_bonus

            # Penalize rug pulls heavily
            if metrics.wallet_rug_pulls > 0:
                rug_penalty = metrics.wallet_rug_pulls * 20
                score -= rug_penalty

        # Wallet maturity
        if metrics.wallet_age_days > 30:
            age_bonus = min(10, metrics.wallet_age_days / 30)
            score += age_bonus

        # Activity level
        if metrics.wallet_transaction_count > 100:
            activity_bonus = min(10, metrics.wallet_transaction_count / 100)
            score += activity_bonus

        # Social presence
        social_bonus = metrics.social_presence_score * 15
        score += social_bonus

        # Development activity
        dev_bonus = metrics.developer_activity_score * 15
        score += dev_bonus

        return min(100.0, max(0.0, score))

    def _score_technical_metrics(self, metrics: TechnicalMetrics) -> float:
        """Score technical analysis (0-100)"""
        score = 50.0  # Start neutral

        # RSI scoring
        if 30 <= metrics.rsi_14 <= 70:
            score += 20  # Good RSI range
        elif metrics.rsi_14 < 30:
            score += 10  # Oversold, potential upside

        # MACD signal
        if metrics.macd_signal > 0:
            score += 15  # Bullish signal

        # Bollinger position
        if 0.3 <= metrics.bollinger_position <= 0.7:
            score += 10  # Good position in bands

        # Momentum
        if metrics.momentum_score > 0:
            momentum_bonus = metrics.momentum_score * 15
            score += momentum_bonus

        # Trend strength
        if metrics.trend_strength > 0:
            trend_bonus = metrics.trend_strength * 10
            score += trend_bonus

        return min(100.0, max(0.0, score))

    def _calculate_confidence(self, social: SocialMetrics, onchain: OnChainMetrics,
                            creator: CreatorMetrics, technical: TechnicalMetrics) -> float:
        """Calculate confidence score based on data completeness"""
        confidence = 0.0

        # Social data completeness
        social_completeness = 0.0
        if social.twitter_followers > 0:
            social_completeness += 0.3
        if social.social_sentiment_score != 0.0:
            social_completeness += 0.4
        if social.mention_velocity > 0:
            social_completeness += 0.3

        # On-chain data completeness  
        onchain_completeness = 0.0
        if onchain.market_cap_usd > 0:
            onchain_completeness += 0.3
        if onchain.liquidity_usd > 0:
            onchain_completeness += 0.3
        if onchain.holder_count > 0:
            onchain_completeness += 0.4

        # Creator data completeness
        creator_completeness = 0.0
        if creator.wallet_age_days > 0:
            creator_completeness += 0.4
        if creator.previous_tokens_created >= 0:
            creator_completeness += 0.6

        # Technical data completeness
        technical_completeness = 0.8  # Technical analysis is usually available

        # Weight by component importance
        confidence = (
            social_completeness * self.weights['social'] +
            onchain_completeness * self.weights['onchain'] + 
            creator_completeness * self.weights['creator'] +
            technical_completeness * self.weights['technical']
        )

        return min(1.0, max(0.0, confidence))

    def _determine_risk_level(self, overall_score: float, onchain: OnChainMetrics,
                            creator: CreatorMetrics) -> RiskLevel:
        """Determine risk level based on score and critical factors"""

        # Critical risk factors
        if not onchain.mint_authority_burned or not onchain.freeze_authority_burned:
            return RiskLevel.VERY_HIGH

        if creator.wallet_rug_pulls > 0:
            return RiskLevel.VERY_HIGH

        if onchain.top10_holder_pct > 80:
            return RiskLevel.VERY_HIGH

        # Score-based risk levels
        if overall_score >= 80:
            return RiskLevel.LOW
        elif overall_score >= 60:
            return RiskLevel.MEDIUM  
        elif overall_score >= 40:
            return RiskLevel.HIGH
        else:
            return RiskLevel.VERY_HIGH

    def _categorize_token(self, symbol: str, name: str) -> TokenCategory:
        """Categorize token based on name and symbol"""
        symbol_lower = symbol.lower()
        name_lower = name.lower()

        # Gaming tokens
        gaming_keywords = ['game', 'play', 'nft', 'metaverse', 'rpg']
        if any(keyword in name_lower for keyword in gaming_keywords):
            return TokenCategory.GAMING

        # DeFi tokens
        defi_keywords = ['defi', 'swap', 'yield', 'farm', 'stake', 'protocol']
        if any(keyword in name_lower for keyword in defi_keywords):
            return TokenCategory.DEFI

        # Memecoins (default for most new tokens)
        meme_keywords = ['doge', 'pepe', 'shib', 'meme', 'inu', 'coin']
        if any(keyword in name_lower for keyword in meme_keywords):
            return TokenCategory.MEMECOIN

        # Default to memecoin for new tokens
        return TokenCategory.MEMECOIN

    def _identify_factors(self, social: SocialMetrics, onchain: OnChainMetrics,
                         creator: CreatorMetrics, technical: TechnicalMetrics) -> Tuple[List[str], List[str], List[str]]:
        """Identify positive factors, negative factors, and warning flags"""

        positive_factors = []
        negative_factors = []
        warning_flags = []

        # Authority checks
        if onchain.mint_authority_burned and onchain.freeze_authority_burned:
            positive_factors.append("Authorities properly burned")
        else:
            warning_flags.append("Token authorities not burned")

        # Liquidity
        if onchain.liquidity_usd > 100000:
            positive_factors.append("High liquidity pool")
        elif onchain.liquidity_usd < 10000:
            negative_factors.append("Low liquidity")

        # Holder distribution
        if onchain.top10_holder_pct < 40:
            positive_factors.append("Good holder distribution")
        elif onchain.top10_holder_pct > 70:
            warning_flags.append("High holder concentration")

        # Creator history
        if creator.previous_success_rate > 0.7:
            positive_factors.append("Creator has good track record")
        elif creator.wallet_rug_pulls > 0:
            warning_flags.append("Creator has rug pull history")

        # Social engagement
        if social.social_sentiment_score > 0.5:
            positive_factors.append("Positive social sentiment")
        elif social.social_sentiment_score < -0.3:
            negative_factors.append("Negative social sentiment")

        # Technical indicators
        if technical.momentum_score > 0.3:
            positive_factors.append("Strong momentum")
        elif technical.momentum_score < -0.3:
            negative_factors.append("Weak momentum")

        return positive_factors, negative_factors, warning_flags

if __name__ == "__main__":
    # Test AI token scorer
    async def test_ai_scorer():
        print("🧪 Testing AI Token Scorer...")

        try:
            scorer = AITokenScorer()

            # Test token scoring
            result = await scorer.score_token(
                mint="TestMint111111111111111111111111111111111",
                symbol="TEST",
                name="Test Token",
                creator_address="Creator111111111111111111111111111111111"
            )

            print(f"✅ Token scored successfully:")
            print(f"  Overall Score: {result.overall_score:.1f}/100")
            print(f"  Risk Level: {result.risk_level.value}")
            print(f"  Confidence: {result.confidence:.2f}")
            print(f"  Category: {result.category.value}")
            print(f"  Positive Factors: {', '.join(result.positive_factors)}")

            if result.warning_flags:
                print(f"  Warning Flags: {', '.join(result.warning_flags)}")

        except Exception as e:
            print(f"❌ AI scorer test failed: {e}")

    # Run the test
    asyncio.run(test_ai_scorer())
