"""
Advanced Analytics and Performance Monitoring System

Comprehensive real-time monitoring, performance analysis, and alerting system
for tracking trading bot performance, risk metrics, and system health.
"""
import asyncio
import json
import time
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timezone, timedelta
from enum import Enum
import sqlite3
from collections import defaultdict, deque
import httpx
import random



class AlertLevel(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class MetricType(Enum):
    """Types of metrics tracked"""
    PERFORMANCE = "performance"
    RISK = "risk"
    EXECUTION = "execution"
    SYSTEM = "system"
    MARKET = "market"

@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics"""
    timestamp: datetime

    # Trading Performance
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate_pct: float = 0.0

    # Financial Metrics
    total_pnl_sol: float = 0.0
    total_pnl_usd: float = 0.0
    realized_pnl: float = 0.0
    unrealized_pnl: float = 0.0

    # Risk Metrics
    max_drawdown_pct: float = 0.0
    current_drawdown_pct: float = 0.0
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    var_95_pct: float = 0.0  # Value at Risk

    # Position Metrics
    current_positions: int = 0
    total_position_value_sol: float = 0.0
    largest_position_pct: float = 0.0
    portfolio_concentration: float = 0.0

    # Execution Metrics
    avg_execution_time_ms: float = 0.0
    slippage_avg_bps: float = 0.0
    success_rate_pct: float = 0.0

    # Volume Metrics
    volume_24h_sol: float = 0.0
    trade_frequency_per_hour: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data

@dataclass
class SystemMetrics:
    """System health and resource metrics"""
    timestamp: datetime

    # Resource Usage
    cpu_usage_pct: float = 0.0
    memory_usage_pct: float = 0.0
    disk_usage_pct: float = 0.0

    # Network
    rpc_latency_ms: float = 0.0
    rpc_success_rate_pct: float = 100.0
    websocket_connections: int = 0

    # Bot Health
    uptime_hours: float = 0.0
    error_count_1h: int = 0
    warning_count_1h: int = 0

    # Database
    database_size_mb: float = 0.0
    query_avg_time_ms: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data

@dataclass
class Alert:
    """System alert"""
    timestamp: datetime
    level: AlertLevel
    category: str
    message: str
    details: Dict[str, Any]
    resolved: bool = False
    resolved_at: Optional[datetime] = None

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['level'] = self.level.value
        if data['resolved_at']:
            data['resolved_at'] = self.resolved_at.isoformat()
        return data

class PerformanceCalculator:
    """
    Advanced performance calculation engine

    Calculates comprehensive trading and risk metrics from trade data
    with proper statistical methods and risk-adjusted measures.
    """

    def __init__(self):
        # Initialize logger
        from .logutil import get_logger
        self.logger = get_logger("performance_calc")

    def calculate_comprehensive_metrics(self, trades: List[Dict[str, Any]], 
                                      current_positions: List[Dict[str, Any]],
                                      sol_price_usd: float = 100.0) -> PerformanceMetrics:
        """Calculate comprehensive performance metrics"""
        try:
            now = datetime.now(timezone.utc)
            metrics = PerformanceMetrics(timestamp=now)

            if not trades:
                return metrics

            # Basic trade metrics
            metrics.total_trades = len(trades)
            winning_trades = [t for t in trades if t.get('pnl_sol', 0) > 0]
            losing_trades = [t for t in trades if t.get('pnl_sol', 0) < 0]

            metrics.winning_trades = len(winning_trades)
            metrics.losing_trades = len(losing_trades)
            metrics.win_rate_pct = (metrics.winning_trades / metrics.total_trades) * 100 if metrics.total_trades > 0 else 0

            # PnL calculations
            pnls_sol = [t.get('pnl_sol', 0) for t in trades]
            metrics.total_pnl_sol = sum(pnls_sol)
            metrics.total_pnl_usd = metrics.total_pnl_sol * sol_price_usd
            metrics.realized_pnl = metrics.total_pnl_sol

            # Unrealized PnL from current positions
            unrealized = sum(pos.get('unrealized_pnl_sol', 0) for pos in current_positions)
            metrics.unrealized_pnl = unrealized

            # Risk metrics
            if len(pnls_sol) > 1:
                metrics.max_drawdown_pct = self._calculate_max_drawdown(pnls_sol)
                metrics.current_drawdown_pct = self._calculate_current_drawdown(pnls_sol)
                metrics.sharpe_ratio = self._calculate_sharpe_ratio(pnls_sol)
                metrics.sortino_ratio = self._calculate_sortino_ratio(pnls_sol)
                metrics.var_95_pct = self._calculate_var(pnls_sol, confidence=0.95)

            # Position metrics
            metrics.current_positions = len(current_positions)
            metrics.total_position_value_sol = sum(pos.get('value_sol', 0) for pos in current_positions)

            if current_positions:
                position_sizes = [pos.get('value_sol', 0) for pos in current_positions]
                metrics.largest_position_pct = (max(position_sizes) / metrics.total_position_value_sol) * 100 if metrics.total_position_value_sol > 0 else 0
                metrics.portfolio_concentration = self._calculate_concentration_index(position_sizes)

            # Execution metrics
            execution_times = [t.get('execution_time_ms', 0) for t in trades if t.get('execution_time_ms')]
            if execution_times:
                metrics.avg_execution_time_ms = np.mean(execution_times)

            slippages = [t.get('slippage_bps', 0) for t in trades if t.get('slippage_bps') is not None]
            if slippages:
                metrics.slippage_avg_bps = np.mean(slippages)

            successful_trades = [t for t in trades if t.get('success', False)]
            metrics.success_rate_pct = (len(successful_trades) / len(trades)) * 100 if trades else 0

            # Volume metrics
            recent_trades = [t for t in trades if t.get('timestamp', datetime.min.replace(tzinfo=timezone.utc)) > now - timedelta(hours=24)]
            metrics.volume_24h_sol = sum(t.get('amount_sol', 0) for t in recent_trades)

            if recent_trades:
                time_span_hours = 24
                metrics.trade_frequency_per_hour = len(recent_trades) / time_span_hours

            return metrics

        except Exception as e:
            self.logger.error(f"Performance calculation failed: {e}")
            return PerformanceMetrics(timestamp=datetime.now(timezone.utc))

    def _calculate_max_drawdown(self, pnls: List[float]) -> float:
        """Calculate maximum drawdown percentage"""
        try:
            cumulative_returns = np.cumsum(pnls)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - running_max) / np.maximum(running_max, 1e-6)  # Avoid division by zero
            return abs(np.min(drawdowns)) * 100  # Convert to percentage
        except Exception:
            return 0.0

    def _calculate_current_drawdown(self, pnls: List[float]) -> float:
        """Calculate current drawdown percentage"""
        try:
            cumulative_returns = np.cumsum(pnls)
            peak = np.max(cumulative_returns)
            current = cumulative_returns[-1]
            return abs((current - peak) / max(peak, 1e-6)) * 100
        except Exception:
            return 0.0

    def _calculate_sharpe_ratio(self, pnls: List[float], risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio"""
        try:
            if len(pnls) < 2:
                return 0.0

            mean_return = np.mean(pnls)
            std_return = np.std(pnls, ddof=1)

            if std_return == 0:
                return 0.0

            # Annualize (assuming daily returns)
            annual_mean = mean_return * 365
            annual_std = std_return * np.sqrt(365)

            return (annual_mean - risk_free_rate) / annual_std
        except Exception:
            return 0.0

    def _calculate_sortino_ratio(self, pnls: List[float], risk_free_rate: float = 0.02) -> float:
        """Calculate Sortino ratio (uses downside deviation)"""
        try:
            if len(pnls) < 2:
                return 0.0

            mean_return = np.mean(pnls)
            negative_returns = [p for p in pnls if p < 0]

            if not negative_returns:
                return float('inf')  # No downside risk

            downside_std = np.std(negative_returns, ddof=1)

            if downside_std == 0:
                return 0.0

            # Annualize
            annual_mean = mean_return * 365
            annual_downside_std = downside_std * np.sqrt(365)

            return (annual_mean - risk_free_rate) / annual_downside_std
        except Exception:
            return 0.0

    def _calculate_var(self, pnls: List[float], confidence: float = 0.95) -> float:
        """Calculate Value at Risk"""
        try:
            if len(pnls) < 10:  # Need reasonable sample size
                return 0.0

            return abs(np.percentile(pnls, (1 - confidence) * 100))
        except Exception:
            return 0.0

    def _calculate_concentration_index(self, position_sizes: List[float]) -> float:
        """Calculate portfolio concentration (Herfindahl-Hirschman Index)"""
        try:
            total = sum(position_sizes)
            if total == 0:
                return 0.0

            weights = [size / total for size in position_sizes]
            hhi = sum(w ** 2 for w in weights)

            # Normalize to 0-100 scale
            return hhi * 100
        except Exception:
            return 0.0

class SystemMonitor:
    """
    System health and resource monitoring

    Monitors system resources, network connectivity, and service health
    to ensure optimal bot performance and early issue detection.
    """

    def __init__(self):
        self.start_time = datetime.now(timezone.utc)

        # Initialize logger
        from .logutil import get_logger
        self.logger = get_logger("system_monitor")

    async def collect_system_metrics(self) -> SystemMetrics:
        """Collect current system health metrics"""
        try:
            now = datetime.now(timezone.utc)
            metrics = SystemMetrics(timestamp=now)

            # Resource usage (simplified - would use psutil in production)
            metrics.cpu_usage_pct = await self._get_cpu_usage()
            metrics.memory_usage_pct = await self._get_memory_usage()
            metrics.disk_usage_pct = await self._get_disk_usage()

            # Network metrics
            metrics.rpc_latency_ms = await self._measure_rpc_latency()
            metrics.rpc_success_rate_pct = await self._calculate_rpc_success_rate()
            metrics.websocket_connections = await self._count_websocket_connections()

            # Bot health
            metrics.uptime_hours = (now - self.start_time).total_seconds() / 3600
            metrics.error_count_1h = await self._count_recent_errors(hours=1)
            metrics.warning_count_1h = await self._count_recent_warnings(hours=1)

            # Database metrics
            metrics.database_size_mb = await self._get_database_size()
            metrics.query_avg_time_ms = await self._measure_database_performance()

            return metrics

        except Exception as e:
            self.logger.error(f"System metrics collection failed: {e}")
            return SystemMetrics(timestamp=datetime.now(timezone.utc))

    async def _get_cpu_usage(self) -> float:
        """Get CPU usage percentage"""
        # Simplified implementation - would use psutil.cpu_percent()
        import random
        return random.uniform(10, 80)

    async def _get_memory_usage(self) -> float:
        """Get memory usage percentage"""
        # Simplified implementation - would use psutil.virtual_memory().percent
        import random
        return random.uniform(30, 70)

    async def _get_disk_usage(self) -> float:
        """Get disk usage percentage"""
        # Simplified implementation - would use psutil.disk_usage('/').percent
        import random
        return random.uniform(20, 60)

    async def _measure_rpc_latency(self) -> float:
        """Measure RPC endpoint latency"""
        try:
            # Would ping actual RPC endpoint
            start_time = time.time()
            # Simulate network call
            await asyncio.sleep(0.05)  # 50ms simulated latency
            return (time.time() - start_time) * 1000
        except Exception:
            return 1000.0  # High latency on error

    async def _calculate_rpc_success_rate(self) -> float:
        """Calculate RPC success rate over recent period"""
        # Would track actual RPC call success/failure rates
        return random.uniform(95, 100)

    async def _count_websocket_connections(self) -> int:
        """Count active WebSocket connections"""
        # Would count actual WebSocket connections
        return random.randint(1, 5)

    async def _count_recent_errors(self, hours: int) -> int:
        """Count errors in recent time period"""
        # Would query log database or error tracking system
        return random.randint(0, 3)

    async def _count_recent_warnings(self, hours: int) -> int:
        """Count warnings in recent time period"""
        # Would query log database
        return random.randint(0, 10)

    async def _get_database_size(self) -> float:
        """Get database size in MB"""
        try:
            # Would check actual database file size
            return 15.5  # Simulated size
        except Exception:
            return 0.0

    async def _measure_database_performance(self) -> float:
        """Measure average database query time"""
        try:
            # Would run test queries and measure performance
            return random.uniform(1, 10)  # 1-10ms
        except Exception:
            return 100.0  # High latency on error

class AlertSystem:
    """
    Advanced alerting system with multiple channels

    Monitors metrics and system health to generate alerts
    with configurable thresholds and notification channels.
    """

    def __init__(self):
        self.alert_rules: List[Dict[str, Any]] = []
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: deque = deque(maxlen=1000)  # Keep last 1000 alerts

        # Alert suppression to prevent spam
        self.alert_cooldowns: Dict[str, datetime] = {}
        self.cooldown_duration = timedelta(minutes=10)

        # Initialize logger
        from .logutil import get_logger
        self.logger = get_logger("alert_system")

        self._setup_default_alert_rules()

    def _setup_default_alert_rules(self):
        """Setup default alert rules"""
        self.alert_rules = [
            # Performance alerts
            {
                'name': 'low_win_rate',
                'condition': lambda m: getattr(m, 'win_rate_pct', 100) < 40,
                'level': AlertLevel.WARNING,
                'category': 'performance',
                'message': 'Win rate below 40%',
                'cooldown_key': 'win_rate'
            },
            {
                'name': 'high_drawdown',
                'condition': lambda m: getattr(m, 'current_drawdown_pct', 0) > 15,
                'level': AlertLevel.ERROR,
                'category': 'risk',
                'message': 'Current drawdown exceeds 15%',
                'cooldown_key': 'drawdown'
            },
            {
                'name': 'execution_latency',
                'condition': lambda m: getattr(m, 'avg_execution_time_ms', 0) > 5000,
                'level': AlertLevel.WARNING,
                'category': 'execution',
                'message': 'Average execution time exceeds 5 seconds',
                'cooldown_key': 'latency'
            },

            # System alerts
            {
                'name': 'high_cpu_usage',
                'condition': lambda m: getattr(m, 'cpu_usage_pct', 0) > 90,
                'level': AlertLevel.WARNING,
                'category': 'system',
                'message': 'CPU usage exceeds 90%',
                'cooldown_key': 'cpu'
            },
            {
                'name': 'high_memory_usage', 
                'condition': lambda m: getattr(m, 'memory_usage_pct', 0) > 85,
                'level': AlertLevel.WARNING,
                'category': 'system',
                'message': 'Memory usage exceeds 85%',
                'cooldown_key': 'memory'
            },
            {
                'name': 'rpc_latency',
                'condition': lambda m: getattr(m, 'rpc_latency_ms', 0) > 1000,
                'level': AlertLevel.ERROR,
                'category': 'network',
                'message': 'RPC latency exceeds 1 second',
                'cooldown_key': 'rpc_latency'
            },
            {
                'name': 'rpc_failure_rate',
                'condition': lambda m: getattr(m, 'rpc_success_rate_pct', 100) < 95,
                'level': AlertLevel.ERROR,
                'category': 'network',
                'message': 'RPC success rate below 95%',
                'cooldown_key': 'rpc_failures'
            }
        ]

    async def check_alerts(self, performance_metrics: PerformanceMetrics,
                          system_metrics: SystemMetrics):
        """Check all alert conditions and generate alerts"""
        try:
            for rule in self.alert_rules:
                try:
                    # Check performance metrics
                    if rule['condition'](performance_metrics):
                        await self._trigger_alert(rule, performance_metrics, 'performance')

                    # Check system metrics  
                    if rule['condition'](system_metrics):
                        await self._trigger_alert(rule, system_metrics, 'system')

                except Exception as e:
                    self.logger.error(f"Alert rule check failed for {rule.get('name', 'unknown')}: {e}")

        except Exception as e:
            self.logger.error(f"Alert checking failed: {e}")

    async def _trigger_alert(self, rule: Dict[str, Any], metrics, source: str):
        """Trigger an alert based on rule"""
        try:
            cooldown_key = rule.get('cooldown_key', rule['name'])

            # Check cooldown
            if cooldown_key in self.alert_cooldowns:
                if datetime.now(timezone.utc) < self.alert_cooldowns[cooldown_key]:
                    return  # Still in cooldown

            # Create alert
            alert = Alert(
                timestamp=datetime.now(timezone.utc),
                level=rule['level'],
                category=rule['category'],
                message=rule['message'],
                details={
                    'rule_name': rule['name'],
                    'source': source,
                    'metrics_snapshot': metrics.to_dict() if hasattr(metrics, 'to_dict') else str(metrics)
                }
            )

            # Store and send alert
            alert_id = f"{rule['name']}_{int(alert.timestamp.timestamp())}"
            self.active_alerts[alert_id] = alert
            self.alert_history.append(alert)

            # Set cooldown
            self.alert_cooldowns[cooldown_key] = datetime.now(timezone.utc) + self.cooldown_duration

            # Send alert through notification channels
            await self._send_alert_notification(alert)

            self.logger.warning(f"Alert triggered: {alert.level.value.upper()} - {alert.message}")

        except Exception as e:
            self.logger.error(f"Alert trigger failed: {e}")

    async def _send_alert_notification(self, alert: Alert):
        """Send alert through configured notification channels"""
        try:
            # Console logging (always enabled)
            log_level = {
                AlertLevel.INFO: self.logger.info,
                AlertLevel.WARNING: self.logger.warning,
                AlertLevel.ERROR: self.logger.error,
                AlertLevel.CRITICAL: self.logger.critical
            }.get(alert.level, self.logger.info)

            log_level(f"[{alert.category.upper()}] {alert.message}")

            # Additional notification channels would go here:
            # - Discord webhook
            # - Slack webhook  
            # - Email
            # - Telegram bot
            # - SMS (for critical alerts)

            # Example Discord webhook (commented out)
            # if alert.level in [AlertLevel.ERROR, AlertLevel.CRITICAL]:
            #     await self._send_discord_webhook(alert)

        except Exception as e:
            self.logger.error(f"Alert notification failed: {e}")

    def get_active_alerts(self) -> List[Alert]:
        """Get all active (unresolved) alerts"""
        return [alert for alert in self.active_alerts.values() if not alert.resolved]

    def get_alert_history(self, hours: int = 24) -> List[Alert]:
        """Get alert history for specified time period"""
        cutoff = datetime.now(timezone.utc) - timedelta(hours=hours)
        return [alert for alert in self.alert_history if alert.timestamp > cutoff]

    def resolve_alert(self, alert_id: str) -> bool:
        """Mark an alert as resolved"""
        try:
            if alert_id in self.active_alerts:
                alert = self.active_alerts[alert_id]
                alert.resolved = True
                alert.resolved_at = datetime.now(timezone.utc)
                self.logger.info(f"Alert resolved: {alert_id}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Alert resolution failed: {e}")
            return False

class RealTimeDashboard:
    """
    Real-time dashboard data provider

    Aggregates and formats metrics for dashboard display
    with historical trends and performance summaries.
    """

    def __init__(self):
        self.metrics_history = {
            'performance': deque(maxlen=1000),
            'system': deque(maxlen=1000)
        }

        # Initialize logger
        from .logutil import get_logger
        self.logger = get_logger("dashboard")

    def update_metrics(self, performance_metrics: PerformanceMetrics,
                      system_metrics: SystemMetrics):
        """Update dashboard with new metrics"""
        try:
            self.metrics_history['performance'].append(performance_metrics)
            self.metrics_history['system'].append(system_metrics)

            self.logger.debug("Dashboard metrics updated")

        except Exception as e:
            self.logger.error(f"Dashboard update failed: {e}")

    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get comprehensive dashboard data"""
        try:
            # Get latest metrics
            latest_perf = self.metrics_history['performance'][-1] if self.metrics_history['performance'] else None
            latest_sys = self.metrics_history['system'][-1] if self.metrics_history['system'] else None

            # Calculate trends
            perf_trends = self._calculate_trends('performance')
            sys_trends = self._calculate_trends('system')

            dashboard_data = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'current_metrics': {
                    'performance': latest_perf.to_dict() if latest_perf else {},
                    'system': latest_sys.to_dict() if latest_sys else {}
                },
                'trends': {
                    'performance': perf_trends,
                    'system': sys_trends
                },
                'summary': self._generate_summary(),
                'health_score': self._calculate_health_score()
            }

            return dashboard_data

        except Exception as e:
            self.logger.error(f"Dashboard data generation failed: {e}")
            return {}

    def _calculate_trends(self, metric_type: str, periods: int = 10) -> Dict[str, float]:
        """Calculate trends for dashboard display"""
        try:
            history = list(self.metrics_history[metric_type])

            if len(history) < 2:
                return {}

            # Get recent data
            recent_data = history[-periods:] if len(history) >= periods else history

            trends = {}

            if metric_type == 'performance':
                # Performance trends
                win_rates = [m.win_rate_pct for m in recent_data]
                if len(win_rates) > 1:
                    trends['win_rate_trend'] = self._calculate_trend_direction(win_rates)

                pnls = [m.total_pnl_sol for m in recent_data]
                if len(pnls) > 1:
                    trends['pnl_trend'] = self._calculate_trend_direction(pnls)

                drawdowns = [m.current_drawdown_pct for m in recent_data]
                if len(drawdowns) > 1:
                    trends['drawdown_trend'] = self._calculate_trend_direction(drawdowns, reverse=True)  # Lower is better

            elif metric_type == 'system':
                # System trends
                cpu_usage = [m.cpu_usage_pct for m in recent_data]
                if len(cpu_usage) > 1:
                    trends['cpu_trend'] = self._calculate_trend_direction(cpu_usage, reverse=True)

                latency = [m.rpc_latency_ms for m in recent_data]
                if len(latency) > 1:
                    trends['latency_trend'] = self._calculate_trend_direction(latency, reverse=True)

            return trends

        except Exception as e:
            self.logger.error(f"Trend calculation failed: {e}")
            return {}

    def _calculate_trend_direction(self, values: List[float], reverse: bool = False) -> float:
        """Calculate trend direction (-1 to +1)"""
        try:
            if len(values) < 2:
                return 0.0

            # Simple linear trend
            x = np.arange(len(values))
            slope = np.polyfit(x, values, 1)[0]

            # Normalize slope to -1 to +1 range
            normalized = np.tanh(slope / np.std(values)) if np.std(values) > 0 else 0

            return -normalized if reverse else normalized

        except Exception:
            return 0.0

    def _generate_summary(self) -> Dict[str, Any]:
        """Generate performance summary"""
        try:
            if not self.metrics_history['performance']:
                return {}

            latest = self.metrics_history['performance'][-1]

            # Overall status
            status = "healthy"
            if latest.current_drawdown_pct > 10:
                status = "warning"
            if latest.current_drawdown_pct > 20:
                status = "critical"

            summary = {
                'status': status,
                'total_trades': latest.total_trades,
                'win_rate': f"{latest.win_rate_pct:.1f}%",
                'total_pnl': f"{latest.total_pnl_sol:.4f} SOL",
                'current_drawdown': f"{latest.current_drawdown_pct:.1f}%",
                'active_positions': latest.current_positions,
                'uptime_hours': (datetime.now(timezone.utc) - 
                               self.metrics_history['performance'][0].timestamp).total_seconds() / 3600 
                               if self.metrics_history['performance'] else 0
            }

            return summary

        except Exception as e:
            self.logger.error(f"Summary generation failed: {e}")
            return {}

    def _calculate_health_score(self) -> float:
        """Calculate overall system health score (0-100)"""
        try:
            if not self.metrics_history['performance'] or not self.metrics_history['system']:
                return 50.0  # Neutral score

            latest_perf = self.metrics_history['performance'][-1]
            latest_sys = self.metrics_history['system'][-1]

            score_factors = []

            # Performance factors (60% weight)
            if latest_perf.win_rate_pct >= 60:
                score_factors.append(('win_rate', 90, 0.2))
            elif latest_perf.win_rate_pct >= 50:
                score_factors.append(('win_rate', 70, 0.2))
            else:
                score_factors.append(('win_rate', 40, 0.2))

            if latest_perf.current_drawdown_pct <= 5:
                score_factors.append(('drawdown', 95, 0.2))
            elif latest_perf.current_drawdown_pct <= 10:
                score_factors.append(('drawdown', 75, 0.2))
            else:
                score_factors.append(('drawdown', 30, 0.2))

            if latest_perf.success_rate_pct >= 95:
                score_factors.append(('execution', 90, 0.2))
            elif latest_perf.success_rate_pct >= 90:
                score_factors.append(('execution', 70, 0.2))
            else:
                score_factors.append(('execution', 40, 0.2))

            # System factors (40% weight)
            if latest_sys.cpu_usage_pct <= 50:
                score_factors.append(('cpu', 90, 0.15))
            elif latest_sys.cpu_usage_pct <= 80:
                score_factors.append(('cpu', 70, 0.15))
            else:
                score_factors.append(('cpu', 40, 0.15))

            if latest_sys.rpc_latency_ms <= 100:
                score_factors.append(('latency', 95, 0.15))
            elif latest_sys.rpc_latency_ms <= 500:
                score_factors.append(('latency', 75, 0.15))
            else:
                score_factors.append(('latency', 30, 0.15))

            if latest_sys.error_count_1h == 0:
                score_factors.append(('errors', 100, 0.1))
            elif latest_sys.error_count_1h <= 2:
                score_factors.append(('errors', 80, 0.1))
            else:
                score_factors.append(('errors', 40, 0.1))

            # Calculate weighted score
            total_score = sum(score * weight for _, score, weight in score_factors)

            return min(100.0, max(0.0, total_score))

        except Exception as e:
            self.logger.error(f"Health score calculation failed: {e}")
            return 50.0

class AnalyticsEngine:
    """
    Main analytics engine orchestrator

    Coordinates all monitoring, alerting, and analysis components
    to provide comprehensive real-time insights into bot performance.
    """

    def __init__(self, db_path: str = "trading_analytics.db"):
        self.db_path = db_path

        # Components
        self.performance_calculator = PerformanceCalculator()
        self.system_monitor = SystemMonitor()
        self.alert_system = AlertSystem()
        self.dashboard = RealTimeDashboard()

        # Monitoring task
        self.monitoring_task: Optional[asyncio.Task] = None
        self.is_running = False
        self.update_interval = 30  # seconds

        # Initialize database
        self._init_database()

        # Initialize logger
        from .logutil import get_logger
        self.logger = get_logger("analytics_engine")

        self.logger.info("Analytics engine initialized")

    def _init_database(self):
        """Initialize analytics database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Performance metrics table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS performance_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        metrics_json TEXT NOT NULL
                    )
                """)

                # System metrics table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS system_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        metrics_json TEXT NOT NULL
                    )
                """)

                # Alerts table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS alerts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        level TEXT NOT NULL,
                        category TEXT NOT NULL,
                        message TEXT NOT NULL,
                        details_json TEXT NOT NULL,
                        resolved INTEGER DEFAULT 0,
                        resolved_at TEXT
                    )
                """)

                conn.commit()

        except Exception as e:
            self.logger.error(f"Database initialization failed: {e}")

    async def start_monitoring(self):
        """Start real-time monitoring"""
        try:
            if self.is_running:
                self.logger.warning("Analytics engine already running")
                return

            self.is_running = True
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())

            self.logger.info("Analytics monitoring started")

        except Exception as e:
            self.logger.error(f"Monitoring start failed: {e}")
            self.is_running = False

    async def stop_monitoring(self):
        """Stop real-time monitoring"""
        try:
            self.is_running = False

            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass

            self.logger.info("Analytics monitoring stopped")

        except Exception as e:
            self.logger.error(f"Monitoring stop failed: {e}")

    async def _monitoring_loop(self):
        """Main monitoring loop"""
        try:
            while self.is_running:
                try:
                    # Collect metrics
                    perf_metrics = await self._collect_performance_metrics()
                    sys_metrics = await self.system_monitor.collect_system_metrics()

                    # Store metrics
                    await self._store_metrics(perf_metrics, sys_metrics)

                    # Update dashboard
                    self.dashboard.update_metrics(perf_metrics, sys_metrics)

                    # Check alerts
                    await self.alert_system.check_alerts(perf_metrics, sys_metrics)

                    self.logger.debug("Analytics cycle completed")

                except Exception as e:
                    self.logger.error(f"Monitoring cycle failed: {e}")

                # Wait for next cycle
                await asyncio.sleep(self.update_interval)

        except asyncio.CancelledError:
            self.logger.info("Monitoring loop cancelled")
        except Exception as e:
            self.logger.error(f"Monitoring loop failed: {e}")

    async def _collect_performance_metrics(self) -> PerformanceMetrics:
        """Collect performance metrics from trading data"""
        try:
            # This would integrate with the actual trading database
            # For now, return simulated metrics

            import random

            metrics = PerformanceMetrics(
                timestamp=datetime.now(timezone.utc),
                total_trades=random.randint(50, 200),
                winning_trades=random.randint(30, 120),
                losing_trades=random.randint(10, 80),
                win_rate_pct=random.uniform(55, 75),
                total_pnl_sol=random.uniform(-2, 10),
                current_drawdown_pct=random.uniform(0, 15),
                current_positions=random.randint(0, 5),
                avg_execution_time_ms=random.uniform(50, 500),
                success_rate_pct=random.uniform(92, 99)
            )

            return metrics

        except Exception as e:
            self.logger.error(f"Performance metrics collection failed: {e}")
            return PerformanceMetrics(timestamp=datetime.now(timezone.utc))

    async def _store_metrics(self, perf_metrics: PerformanceMetrics, 
                           sys_metrics: SystemMetrics):
        """Store metrics in database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Store performance metrics
                conn.execute(
                    "INSERT INTO performance_metrics (timestamp, metrics_json) VALUES (?, ?)",
                    (perf_metrics.timestamp.isoformat(), json.dumps(perf_metrics.to_dict()))
                )

                # Store system metrics
                conn.execute(
                    "INSERT INTO system_metrics (timestamp, metrics_json) VALUES (?, ?)",
                    (sys_metrics.timestamp.isoformat(), json.dumps(sys_metrics.to_dict()))
                )

                conn.commit()

        except Exception as e:
            self.logger.error(f"Metrics storage failed: {e}")

    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get real-time dashboard data"""
        return self.dashboard.get_dashboard_data()

    def get_active_alerts(self) -> List[Alert]:
        """Get active alerts"""
        return self.alert_system.get_active_alerts()

    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get performance summary for specified period"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cutoff = (datetime.now(timezone.utc) - timedelta(hours=hours)).isoformat()

                cursor = conn.execute("""
                    SELECT metrics_json FROM performance_metrics 
                    WHERE timestamp > ? 
                    ORDER BY timestamp DESC
                """, (cutoff,))

                records = cursor.fetchall()

                if not records:
                    return {}

                # Parse metrics and calculate summary
                metrics_data = [json.loads(record[0]) for record in records]

                summary = {
                    'period_hours': hours,
                    'total_snapshots': len(metrics_data),
                    'latest_win_rate': metrics_data[0].get('win_rate_pct', 0),
                    'avg_win_rate': np.mean([m.get('win_rate_pct', 0) for m in metrics_data]),
                    'latest_pnl': metrics_data[0].get('total_pnl_sol', 0),
                    'max_drawdown': max([m.get('current_drawdown_pct', 0) for m in metrics_data]),
                    'avg_execution_time': np.mean([m.get('avg_execution_time_ms', 0) for m in metrics_data])
                }

                return summary

        except Exception as e:
            self.logger.error(f"Performance summary generation failed: {e}")
            return {}

if __name__ == "__main__":
    # Test analytics engine
    async def test_analytics():
        print("🧪 Testing Analytics & Monitoring System...")

        try:
            # Create analytics engine
            engine = AnalyticsEngine()

            # Start monitoring
            await engine.start_monitoring()
            print("✅ Monitoring started")

            # Let it run for a few cycles
            await asyncio.sleep(10)

            # Get dashboard data
            dashboard_data = engine.get_dashboard_data()
            print(f"✅ Dashboard data: Health score = {dashboard_data.get('health_score', 0):.1f}")

            # Check alerts
            alerts = engine.get_active_alerts()
            print(f"✅ Active alerts: {len(alerts)}")

            # Get performance summary
            summary = engine.get_performance_summary(hours=1)
            print(f"✅ Performance summary: {summary.get('total_snapshots', 0)} data points")

            # Stop monitoring
            await engine.stop_monitoring()
            print("✅ Monitoring stopped")

        except Exception as e:
            print(f"❌ Analytics test failed: {e}")

    # Run the test
    asyncio.run(test_analytics())
