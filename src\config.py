"""
Solana Memecoin Sniper Bot - Configuration Module

This module provides type-safe configuration management using Pydantic,
loading settings from environment variables with proper validation and defaults.
"""
import os
from typing import Optional, List
from pydantic import BaseModel, Field, validator
from dotenv import load_dotenv
import json

# Load environment variables from .env file


class SolanaConfig(BaseModel):
    sol_rpc_url: str = Field(
        default="https://api.devnet.solana.com",
        description="Solana RPC HTTP endpoint"
    )
    sol_ws_url: str = Field(
        default="wss://api.devnet.solana.com",
        description="Solana WebSocket endpoint"
    )
    sol_secret_key_json: str = Field(
        ..., description="Solana wallet secret key as JSON array string"
    )

    @validator('sol_secret_key_json')
    def validate_secret_key(cls, v):
        try:
            key_array = json.loads(v)
            if not isinstance(key_array, list) or len(key_array) != 64:
                raise ValueError("Secret key must be a JSON array of 64 integers")
            return v
        except json.JSONDecodeError:
            raise ValueError("Invalid JSON format for secret key")


class TradingConfig(BaseModel):
    """Trading parameters and risk limits"""

    # Position sizing
    trade_notional_sol: float = Field(
        default=0.02,
        ge=0.001,
        le=1.0,
        description="SOL amount per trade (0.02 ≈ $5)"
    )
    max_position_pct: float = Field(
        default=20.0,
        ge=1.0,
        le=50.0,
        description="Maximum position size as % of portfolio"
    )

    # Risk limits
    max_slippage_bps: int = Field(
        default=50,
        ge=1,
        le=1000,
        description="Maximum allowed slippage in basis points"
    )
    max_price_impact_pct: float = Field(
        default=1.0,
        ge=0.1,
        le=10.0,
        description="Maximum allowed price impact percentage"
    )

    # Liquidity requirements
    min_liquidity_sol: float = Field(
        default=5.0,
        ge=0.1,
        le=100.0,
        description="Minimum liquidity required in SOL"
    )
    min_mcap_sol: float = Field(
        default=10.0,
        ge=1.0,
        le=10000.0,
        description="Minimum market cap required in SOL"
    )

    # Holder concentration limits
    max_top10_holder_pct: float = Field(
        default=60.0,
        ge=10.0,
        le=95.0,
        description="Maximum top 10 holders concentration %"
    )

class SafetyConfig(BaseModel):
    """Safety and rug check configuration"""

    require_burned_mint_auth: bool = Field(
        default=True,
        description="Require burned mint authority"
    )
    require_burned_freeze_auth: bool = Field(
        default=True,
        description="Require burned freeze authority"
    )

    # Strategy parameters
    tp_pct: float = Field(
        default=5.0,
        ge=0.5,
        le=100.0,
        description="Take profit percentage"
    )
    sl_pct: float = Field(
        default=3.0,
        ge=0.5,
        le=50.0,
        description="Stop loss percentage"
    )
    max_hold_minutes: int = Field(
        default=10,
        ge=1,
        le=1440,
        description="Maximum hold time in minutes"
    )

class SystemConfig(BaseModel):
    """System operation configuration"""

    mode: str = Field(
        default="dryrun",
        description="Operation mode: 'dryrun' or 'live'"
    )

    @validator('mode')
    def validate_mode(cls, v):
        if v.lower() not in ['dryrun', 'live']:
            raise ValueError("Mode must be 'dryrun' or 'live'")
        return v.lower()

    log_level: str = Field(
        default="INFO",
        description="Logging level"
    )

    @validator('log_level')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v.upper()

class APIConfig(BaseModel):
    """External API configuration"""

    birdeye_api_key: Optional[str] = Field(
        default=None,
        description="Birdeye API key for enhanced data"
    )
    quicknode_endpoint: Optional[str] = Field(
        default=None,
        description="QuickNode RPC endpoint"
    )

    # Program IDs
    pumpfun_program_id: str = Field(
        default="6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P",
        description="Pump.fun program ID"
    )

class CircuitBreakerConfig(BaseModel):
    """Circuit breaker and system protection"""

    max_consecutive_failures: int = Field(
        default=3,
        ge=1,
        le=10,
        description="Max consecutive failures before circuit breaker"
    )
    cooldown_minutes: int = Field(
        default=30,
        ge=5,
        le=1440,
        description="Circuit breaker cooldown period in minutes"
    )

class BotConfig(BaseModel):
    """Main configuration class combining all sub-configurations"""

    solana: SolanaConfig
    trading: TradingConfig
    safety: SafetyConfig
    system: SystemConfig
    api: APIConfig
    circuit_breaker: CircuitBreakerConfig

    class Config:
        env_file = ".env"
        env_nested_delimiter = "__"

def load_config() -> BotConfig:
    """Load and validate configuration from environment variables"""

    try:
        config = BotConfig(
            solana=SolanaConfig(
                sol_rpc_url=os.getenv("SOL_RPC_URL", "https://api.mainnet-beta.solana.com"),
                sol_ws_url=os.getenv("SOL_WS_URL", "wss://api.mainnet-beta.solana.com"),
                sol_secret_key_json=os.getenv("SOL_SECRET_KEY_JSON", "")
            ),
            trading=TradingConfig(
                trade_notional_sol=float(os.getenv("TRADE_NOTIONAL_SOL", "0.02")),
                max_position_pct=float(os.getenv("MAX_POSITION_PCT", "20.0")),
                max_slippage_bps=int(os.getenv("MAX_SLIPPAGE_BPS", "50")),
                max_price_impact_pct=float(os.getenv("MAX_PRICE_IMPACT_PCT", "1.0")),
                min_liquidity_sol=float(os.getenv("MIN_LIQUIDITY_SOL", "5.0")),
                min_mcap_sol=float(os.getenv("MIN_MCAP_SOL", "10.0")),
                max_top10_holder_pct=float(os.getenv("MAX_TOP10_HOLDER_PCT", "60.0"))
            ),
            safety=SafetyConfig(
                require_burned_mint_auth=os.getenv("REQUIRE_BURNED_MINT_AUTH", "true").lower() == "true",
                require_burned_freeze_auth=os.getenv("REQUIRE_BURNED_FREEZE_AUTH", "true").lower() == "true",
                tp_pct=float(os.getenv("TP_PCT", "5.0")),
                sl_pct=float(os.getenv("SL_PCT", "3.0")),
                max_hold_minutes=int(os.getenv("MAX_HOLD_MINUTES", "10"))
            ),
            system=SystemConfig(
                mode=os.getenv("MODE", "dryrun"),
                log_level=os.getenv("LOG_LEVEL", "INFO")
            ),
            api=APIConfig(
                birdeye_api_key=os.getenv("BIRDEYE_API_KEY"),
                quicknode_endpoint=os.getenv("QUICKNODE_ENDPOINT"),
                pumpfun_program_id=os.getenv("PUMPFUN_PROGRAM_ID", "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P")
            ),
            circuit_breaker=CircuitBreakerConfig(
                max_consecutive_failures=int(os.getenv("MAX_CONSECUTIVE_FAILURES", "3")),
                cooldown_minutes=int(os.getenv("CIRCUIT_BREAKER_COOLDOWN_MINUTES", "30"))
            )
        )
        return config
    except Exception as e:
        raise RuntimeError(f"Configuration validation failed: {e}")

# Global configuration instance
CONFIG = None

def get_config() -> BotConfig:
    """Get the global configuration instance, loading if necessary"""
    global CONFIG
    if CONFIG is None:
        CONFIG = load_config()
    return CONFIG

def is_live_mode() -> bool:
    """Check if the bot is running in live trading mode (supports enum or str)"""
    mode = get_config().system.mode
    try:
        # Enum case
        return getattr(mode, "value", mode) in ("live", "LIVE", "Live")
    except Exception:
        return str(mode).lower() == "live"

def validate_live_mode():
    """Validate live mode configuration and show warnings"""
    if not is_live_mode():
        return

    config = get_config()

    print("⚠️  WARNING: LIVE TRADING MODE ENABLED ⚠️")
    print("This will use real funds and execute real trades!")
    print(f"Trade size: {config.trading.trade_notional_sol} SOL")
    print(f"Max slippage: {config.trading.max_slippage_bps} bps")
    print(f"Max price impact: {config.trading.max_price_impact_pct}%")
    print()

    # Require explicit confirmation for live mode
    confirmation = input("Type 'I UNDERSTAND THE RISKS' to continue: ")
    if confirmation != "I UNDERSTAND THE RISKS":
        print("Live mode not confirmed. Exiting...")
        exit(1)
"""
Production-Ready Configuration System

Real Pydantic-based configuration with proper validation for live trading.
"""
import os
import json
from typing import Optional, List
from pydantic import BaseModel, Field, validator
from enum import Enum

class TradingMode(str, Enum):
    """Trading modes"""
    DRYRUN = "dryrun"
    LIVE = "live"
    MICRO_OPTIMIZED = "micro_optimized"
    BACKTEST = "backtest"

class NetworkCluster(str, Enum):
    """Solana network clusters"""
    MAINNET = "mainnet"
    DEVNET = "devnet"
    TESTNET = "testnet"
    LOCALNET = "localnet"

class SolanaConfig(BaseModel):
    """Solana network and wallet configuration"""

    # Network endpoints
    sol_rpc_url: str = Field(
        default="https://api.devnet.solana.com",
        description="Solana RPC HTTP endpoint"
    )
    sol_ws_url: str = Field(
        default="wss://api.devnet.solana.com",
        description="Solana WebSocket endpoint"
    )

    # Wallet configuration
    sol_secret_key_json: str = Field(
        ..., description="Solana wallet secret key as JSON array string"
    )

    # Network settings
    cluster: NetworkCluster = Field(
        default=NetworkCluster.DEVNET,
        description="Solana cluster to connect to"
    )

    # RPC settings
    rpc_timeout: int = Field(default=30, description="RPC timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum RPC retries")

    @validator('sol_secret_key_json')
    def validate_secret_key(cls, v):
        if not v:
            raise ValueError("SOL_SECRET_KEY_JSON is required")
        try:
            key_array = json.loads(v)
            if not isinstance(key_array, list) or len(key_array) != 64:
                raise ValueError("Secret key must be a JSON array of 64 integers")
            # Validate each element is an integer in valid range
            for i, val in enumerate(key_array):
                if not isinstance(val, int) or val < 0 or val > 255:
                    raise ValueError(f"Secret key element {i} must be integer 0-255, got {val}")
            return v
        except json.JSONDecodeError:
            raise ValueError("Invalid JSON format for secret key")

    @validator('sol_rpc_url')
    def validate_rpc_url(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError("RPC URL must start with http:// or https://")
        return v

    @validator('sol_ws_url')
    def validate_ws_url(cls, v):
        if not v.startswith(('ws://', 'wss://')):
            raise ValueError("WebSocket URL must start with ws:// or wss://")
        return v

class TradingConfig(BaseModel):
    """Trading strategy configuration"""

    # Position sizing
    trade_notional_sol: float = Field(
        default=0.05, ge=0.001, le=100.0,
        description="Default trade size in SOL"
    )
    target_trade_size_usd: float = Field(
        default=5.0, ge=1.0, le=10000.0,
        description="Target trade size in USD"
    )

    # Risk management
    max_position_size_pct: float = Field(
        default=10.0, ge=0.1, le=50.0,
        description="Maximum position size as percentage of portfolio"
    )
    max_daily_risk_pct: float = Field(
        default=5.0, ge=0.1, le=20.0,
        description="Maximum daily risk as percentage of portfolio"
    )

    # Execution parameters
    max_slippage_bps: int = Field(
        default=50, ge=1, le=1000,
        description="Maximum slippage in basis points"
    )
    max_execution_delay_ms: int = Field(
        default=500, ge=100, le=5000,
        description="Maximum execution delay in milliseconds"
    )

    # Profit/Loss targets
    stop_loss_pct: float = Field(
        default=8.0, ge=1.0, le=50.0,
        description="Stop loss percentage"
    )
    take_profit_pct: float = Field(
        default=200.0, ge=10.0, le=1000.0,
        description="Take profit percentage"
    )

    # Fee management
    max_total_fee_pct: float = Field(
        default=1.5, ge=0.1, le=10.0,
        description="Maximum total fees as percentage of trade"
    )

    # Compatibility fields for legacy code paths (used in dryrun and docs)
    # Maximum acceptable price impact percentage for a trade
    max_price_impact_pct: float = Field(
        default=1.0, ge=0.1, le=10.0,
        description="Maximum allowed price impact percentage"
    )
    # Minimum required on-chain liquidity for a token (in SOL)
    min_liquidity_sol: float = Field(
        default=5.0, ge=0.1, le=1000.0,
        description="Minimum liquidity required in SOL"
    )
    # Minimum market cap in SOL for eligibility
    min_mcap_sol: float = Field(
        default=10.0, ge=0.1, le=100000.0,
        description="Minimum market cap required in SOL"
    )
    # Maximum top-10 holder concentration percentage
    max_top10_holder_pct: float = Field(
        default=60.0, ge=5.0, le=100.0,
        description="Maximum top 10 holders concentration percentage"
    )

class AIConfig(BaseModel):
    """AI scoring configuration"""

    enable_ai_scoring: bool = Field(default=True, description="Enable AI token scoring")
    ai_confidence_threshold: float = Field(
        default=0.85, ge=0.1, le=1.0,
        description="Minimum AI confidence for trades"
    )
    ai_score_threshold: float = Field(
        default=75.0, ge=1.0, le=100.0,
        description="Minimum AI score for trades"
    )

    # Social analysis
    enable_social_analysis: bool = Field(default=True, description="Enable social sentiment analysis")
    social_weight: float = Field(default=0.3, ge=0.0, le=1.0, description="Social analysis weight")

    # Technical analysis
    enable_technical_analysis: bool = Field(default=True, description="Enable technical analysis")
    technical_weight: float = Field(default=0.3, ge=0.0, le=1.0, description="Technical analysis weight")

    # Creator analysis
    enable_creator_analysis: bool = Field(default=True, description="Enable creator analysis")
    creator_weight: float = Field(default=0.4, ge=0.0, le=1.0, description="Creator analysis weight")

class MEVConfig(BaseModel):
    """MEV protection configuration"""

    enable_jito_bundles: bool = Field(default=True, description="Enable Jito MEV protection")
    mev_protection_level: str = Field(
        default="high", pattern="^(low|medium|high|critical)$",
        description="MEV protection level"
    )
    max_jito_tip_usd: float = Field(
        default=0.25, ge=0.01, le=5.0,
        description="Maximum Jito tip in USD"
    )

    # Jito endpoints
    jito_rpc_url: str = Field(
        default="https://mainnet.block-engine.jito.wtf/api/v1/bundles",
        description="Jito block engine URL"
    )
    jito_tip_accounts: List[str] = Field(
        default=[
            "96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNLiZ4x1",
            "HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe",
            "Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY"
        ],
        description="Jito tip account addresses"
    )

class AdvancedFeaturesConfig(BaseModel):
    """Advanced features configuration"""

    # Copy trading
    enable_copy_trading: bool = Field(default=False, description="Enable copy trading")
    max_copy_wallets: int = Field(default=5, ge=1, le=20, description="Maximum wallets to copy")
    copy_position_scale: float = Field(
        default=0.3, ge=0.1, le=1.0,
        description="Position scaling for copy trades"
    )

    # Volume generation
    enable_volume_gen: bool = Field(default=False, description="Enable volume generation")
    volume_wallet_count: int = Field(default=3, ge=1, le=10, description="Number of volume wallets")

    # Fee arbitrage
    enable_fee_arbitrage: bool = Field(default=True, description="Enable fee arbitrage")
    min_arbitrage_savings_usd: float = Field(
        default=0.05, ge=0.01, le=1.0,
        description="Minimum arbitrage savings in USD"
    )

class MonitoringConfig(BaseModel):
    """Monitoring and alerting configuration"""

    enable_analytics: bool = Field(default=True, description="Enable analytics")
    analytics_update_interval: int = Field(
        default=30, ge=5, le=300,
        description="Analytics update interval in seconds"
    )

    # Database
    database_path: str = Field(default="trading_analytics.db", description="Analytics database path")

    # Logging
    log_level: str = Field(
        default="INFO", pattern="^(DEBUG|INFO|WARNING|ERROR|CRITICAL)$",
        description="Logging level"
    )
    log_file: Optional[str] = Field(default=None, description="Log file path")

    # Alerts
    alert_channels: List[str] = Field(
        default=["console"], description="Alert notification channels"
    )
    discord_webhook_url: Optional[str] = Field(default=None, description="Discord webhook URL")
    telegram_bot_token: Optional[str] = Field(default=None, description="Telegram bot token")
    telegram_chat_id: Optional[str] = Field(default=None, description="Telegram chat ID")

class SystemConfig(BaseModel):
    """System operation configuration"""

    mode: TradingMode = Field(default=TradingMode.DRYRUN, description="Trading mode")

    # Performance
    max_concurrent_trades: int = Field(
        default=3, ge=1, le=10,
        description="Maximum concurrent trades"
    )
    enable_performance_mode: bool = Field(default=True, description="Enable performance optimizations")

    # Safety
    enable_circuit_breakers: bool = Field(default=True, description="Enable circuit breakers")
    circuit_breaker_loss_pct: float = Field(
        default=10.0, ge=1.0, le=50.0,
        description="Circuit breaker loss percentage"
    )

class BotConfig(BaseModel):
    """Main bot configuration container"""

    solana: SolanaConfig
    trading: TradingConfig
    ai: AIConfig
    mev: MEVConfig
    advanced: AdvancedFeaturesConfig
    monitoring: MonitoringConfig
    system: SystemConfig

    class Config:
        env_prefix = ""
        case_sensitive = False

def get_config() -> BotConfig:
    """Load configuration from environment variables"""

    # Load environment variables
    from dotenv import load_dotenv
    try:
        load_dotenv()
    except Exception as e:
        # Log the error but continue without .env file
        import logging
        logging.warning(f"Failed to load .env file: {e}. Continuing with default configuration.")

    # Solana configuration
    solana_config = SolanaConfig(
        sol_rpc_url=os.getenv("SOL_RPC_URL", "https://api.devnet.solana.com"),
        sol_ws_url=os.getenv("SOL_WS_URL", "wss://api.devnet.solana.com"),
        sol_secret_key_json=os.getenv("SOL_SECRET_KEY_JSON", ""),
        cluster=NetworkCluster(os.getenv("SOLANA_CLUSTER", "devnet")),
        rpc_timeout=int(os.getenv("RPC_TIMEOUT", "30")),
        max_retries=int(os.getenv("RPC_MAX_RETRIES", "3"))
    )

    # Trading configuration
    trading_config = TradingConfig(
        trade_notional_sol=float(os.getenv("TRADE_NOTIONAL_SOL", "0.05")),
        target_trade_size_usd=float(os.getenv("TARGET_TRADE_SIZE_USD", "5.0")),
        max_position_size_pct=float(os.getenv("MAX_POSITION_SIZE_PCT", "10.0")),
        max_daily_risk_pct=float(os.getenv("MAX_DAILY_RISK_PCT", "5.0")),
        max_slippage_bps=int(os.getenv("MAX_SLIPPAGE_BPS", "50")),
        max_execution_delay_ms=int(os.getenv("MAX_EXECUTION_DELAY_MS", "500")),
        stop_loss_pct=float(os.getenv("STOP_LOSS_PCT", "8.0")),
        take_profit_pct=float(os.getenv("TAKE_PROFIT_PCT", "200.0")),
        max_total_fee_pct=float(os.getenv("MAX_TOTAL_FEE_PCT", "1.5"))
    )

    # AI configuration
    ai_config = AIConfig(
        enable_ai_scoring=os.getenv("ENABLE_AI_SCORING", "true").lower() == "true",
        ai_confidence_threshold=float(os.getenv("AI_CONFIDENCE_THRESHOLD", "0.85")),
        ai_score_threshold=float(os.getenv("AI_SCORE_THRESHOLD", "75.0")),
        enable_social_analysis=os.getenv("ENABLE_SOCIAL_ANALYSIS", "true").lower() == "true",
        social_weight=float(os.getenv("SOCIAL_WEIGHT", "0.3")),
        enable_technical_analysis=os.getenv("ENABLE_TECHNICAL_ANALYSIS", "true").lower() == "true",
        technical_weight=float(os.getenv("TECHNICAL_WEIGHT", "0.3")),
        enable_creator_analysis=os.getenv("ENABLE_CREATOR_ANALYSIS", "true").lower() == "true",
        creator_weight=float(os.getenv("CREATOR_WEIGHT", "0.4"))
    )

    # MEV configuration
    mev_config = MEVConfig(
        enable_jito_bundles=os.getenv("ENABLE_JITO_BUNDLES", "true").lower() == "true",
        mev_protection_level=os.getenv("MEV_PROTECTION_LEVEL", "high"),
        max_jito_tip_usd=float(os.getenv("MAX_JITO_TIP_USD", "0.25")),
        jito_rpc_url=os.getenv("JITO_RPC_URL", "https://mainnet.block-engine.jito.wtf/api/v1/bundles")
    )

    # Advanced features configuration
    advanced_config = AdvancedFeaturesConfig(
        enable_copy_trading=os.getenv("ENABLE_COPY_TRADING", "false").lower() == "true",
        max_copy_wallets=int(os.getenv("MAX_COPY_WALLETS", "5")),
        copy_position_scale=float(os.getenv("COPY_POSITION_SCALE", "0.3")),
        enable_volume_gen=os.getenv("ENABLE_VOLUME_GEN", "false").lower() == "true",
        volume_wallet_count=int(os.getenv("VOLUME_WALLET_COUNT", "3")),
        enable_fee_arbitrage=os.getenv("ENABLE_FEE_ARBITRAGE", "true").lower() == "true",
        min_arbitrage_savings_usd=float(os.getenv("MIN_ARBITRAGE_SAVINGS_USD", "0.05"))
    )

    # Monitoring configuration
    monitoring_config = MonitoringConfig(
        enable_analytics=os.getenv("ENABLE_ANALYTICS", "true").lower() == "true",
        analytics_update_interval=int(os.getenv("ANALYTICS_UPDATE_INTERVAL", "30")),
        database_path=os.getenv("DATABASE_PATH", "trading_analytics.db"),
        log_level=os.getenv("LOG_LEVEL", "INFO"),
        log_file=os.getenv("LOG_FILE"),
        discord_webhook_url=os.getenv("DISCORD_WEBHOOK_URL"),
        telegram_bot_token=os.getenv("TELEGRAM_BOT_TOKEN"),
        telegram_chat_id=os.getenv("TELEGRAM_CHAT_ID")
    )

    # System configuration
    system_config = SystemConfig(
        mode=TradingMode(os.getenv("MODE", "dryrun")),
        max_concurrent_trades=int(os.getenv("MAX_CONCURRENT_TRADES", "3")),
        enable_performance_mode=os.getenv("ENABLE_PERFORMANCE_MODE", "true").lower() == "true",
        enable_circuit_breakers=os.getenv("ENABLE_CIRCUIT_BREAKERS", "true").lower() == "true",
        circuit_breaker_loss_pct=float(os.getenv("CIRCUIT_BREAKER_LOSS_PCT", "10.0"))
    )

    return BotConfig(
        solana=solana_config,
        trading=trading_config,
        ai=ai_config,
        mev=mev_config,
        advanced=advanced_config,
        monitoring=monitoring_config,
        system=system_config
    )

# Utility functions
def get_network_endpoint(cluster: NetworkCluster, endpoint_type: str = "rpc") -> str:
    """Get network endpoint for cluster"""
    endpoints = {
        NetworkCluster.MAINNET: {
            "rpc": "https://api.mainnet-beta.solana.com",
            "ws": "wss://api.mainnet-beta.solana.com"
        },
        NetworkCluster.DEVNET: {
            "rpc": "https://api.devnet.solana.com",
            "ws": "wss://api.devnet.solana.com"
        },
        NetworkCluster.TESTNET: {
            "rpc": "https://api.testnet.solana.com",
            "ws": "wss://api.testnet.solana.com"
        },
        NetworkCluster.LOCALNET: {
            "rpc": "http://127.0.0.1:8899",
            "ws": "ws://127.0.0.1:8900"
        }
    }

    return endpoints[cluster][endpoint_type]

def validate_config(config: BotConfig) -> List[str]:
    """Validate configuration and return any warnings"""
    warnings = []

    # Check for live mode on mainnet
    if config.system.mode == TradingMode.LIVE and config.solana.cluster == NetworkCluster.MAINNET:
        if config.trading.target_trade_size_usd > 50:
            warnings.append("Large trade size on mainnet - consider starting smaller")

    # Check MEV protection settings
    if not config.mev.enable_jito_bundles and config.system.mode == TradingMode.LIVE:
        warnings.append("MEV protection disabled in live mode - consider enabling")

    # Check AI thresholds
    if config.ai.ai_confidence_threshold < 0.7:
        warnings.append("Low AI confidence threshold - may result in poor trade selection")

    # Check risk settings
    if config.trading.max_daily_risk_pct > 10:
        warnings.append("High daily risk limit - consider reducing for safety")

    return warnings

if __name__ == "__main__":
    # Test configuration loading
    try:
        config = get_config()
        warnings = validate_config(config)

        print("✅ Configuration loaded successfully")
        print(f"Mode: {config.system.mode}")
        print(f"Cluster: {config.solana.cluster}")
        print(f"Target Trade Size: ${config.trading.target_trade_size_usd}")

        if warnings:
            print("⚠️ Configuration warnings:")
            for warning in warnings:
                print(f"  - {warning}")

    except Exception as e:
        print(f"❌ Configuration error: {e}")

if __name__ == "__main__":
    # Test configuration loading
    try:
        config = load_config()
        print("✅ Configuration loaded successfully!")
        print(f"Mode: {config.system.mode}")
        print(f"RPC URL: {config.solana.sol_rpc_url}")
        print(f"Trade size: {config.trading.trade_notional_sol} SOL")
    except Exception as e:
        print(f"❌ Configuration error: {e}")
