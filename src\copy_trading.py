"""
Advanced Copy Trading System

Comprehensive copy trading engine that tracks smart money wallets,
analyzes their performance, and automatically executes trades based
on their activity with intelligent position sizing and risk management.
"""
import asyncio
import json
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, asdict
from datetime import datetime, timezone, timedelta
from enum import Enum
import httpx



class WalletRating(Enum):
    """Wallet performance rating"""
    S_TIER = "s_tier"      # >90% win rate, exceptional performance
    A_TIER = "a_tier"      # >75% win rate, great performance  
    B_TIER = "b_tier"      # >60% win rate, good performance
    C_TIER = "c_tier"      # >45% win rate, average performance
    D_TIER = "d_tier"      # <45% win rate, poor performance

class CopyStrategy(Enum):
    """Copy trading strategy types"""
    MIRROR = "mirror"           # Copy exact positions and timing
    SCALED = "scaled"           # Copy with position size scaling
    SELECTIVE = "selective"     # Copy only high-confidence trades
    DELAYED = "delayed"         # Copy with slight delay for confirmation

@dataclass
class TradeEvent:
    """Individual trade event from tracked wallet"""
    wallet_address: str
    timestamp: datetime
    token_mint: str
    token_symbol: str
    side: str  # 'buy' or 'sell'
    amount_sol: float
    amount_tokens: float
    price_per_token: float
    tx_signature: str
    success: bool
    gas_fee_sol: float

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data

@dataclass 
class WalletMetrics:
    """Comprehensive wallet performance metrics"""
    address: str
    first_seen: datetime
    last_activity: datetime

    # Performance metrics
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate_pct: float = 0.0

    # Financial metrics
    total_volume_sol: float = 0.0
    total_pnl_sol: float = 0.0
    avg_return_pct: float = 0.0
    max_drawdown_pct: float = 0.0
    sharpe_ratio: float = 0.0

    # Risk metrics
    avg_position_size_sol: float = 0.0
    max_position_size_sol: float = 0.0
    portfolio_diversity_score: float = 0.0
    risk_score: float = 0.0  # 0-1, higher is riskier

    # Behavioral metrics
    avg_hold_time_hours: float = 0.0
    trading_frequency_per_day: float = 0.0
    preferred_market_cap_range: str = ""
    success_in_market_conditions: Dict[str, float] = None

    # Social metrics
    follower_count: int = 0
    social_influence_score: float = 0.0

    # Rating and confidence
    rating: WalletRating = WalletRating.D_TIER
    confidence_score: float = 0.0  # 0-1, based on data completeness

    def __post_init__(self):
        if self.success_in_market_conditions is None:
            self.success_in_market_conditions = {}

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['first_seen'] = self.first_seen.isoformat()
        data['last_activity'] = self.last_activity.isoformat()
        data['rating'] = self.rating.value
        return data

@dataclass
class CopyTradeDecision:
    """Decision result for copy trading"""
    should_copy: bool
    reason: str
    confidence: float  # 0-1
    suggested_position_size_sol: float
    max_slippage_bps: int
    urgency_level: str  # 'low', 'medium', 'high', 'critical'
    risk_factors: List[str]

class WalletTracker:
    """
    Advanced wallet tracking and analysis system

    Monitors specified wallets for trading activity and maintains
    comprehensive performance metrics and behavioral analysis.
    """

    def __init__(self, rpc_client):
        self.rpc_client = rpc_client
        self.tracked_wallets: Dict[str, WalletMetrics] = {}
        self.trade_history: Dict[str, List[TradeEvent]] = {}
        self.subscription_tasks: Dict[str, asyncio.Task] = {}

        # Analysis parameters
        self.min_trade_size_sol = 0.01  # Ignore micro trades
        self.analysis_window_days = 30  # Performance analysis window

        # HTTP client for external APIs
        self.http_client = httpx.AsyncClient(timeout=30.0)

        # Initialize logger
        from .logutil import get_logger
        self.logger = get_logger("wallet_tracker")

        self.logger.info("Wallet tracker initialized")

    async def add_wallet_to_track(self, wallet_address: str, 
                                 initial_rating: Optional[WalletRating] = None) -> bool:
        """
        Add a wallet to tracking list

        Args:
            wallet_address: Solana wallet address to track
            initial_rating: Optional initial rating (will be calculated if not provided)

        Returns:
            True if successfully added, False otherwise
        """
        try:
            if wallet_address in self.tracked_wallets:
                self.logger.info(f"Wallet {wallet_address} already being tracked")
                return True

            self.logger.info(f"Adding wallet to tracking: {wallet_address}")

            # Initialize metrics
            metrics = WalletMetrics(
                address=wallet_address,
                first_seen=datetime.now(timezone.utc),
                last_activity=datetime.now(timezone.utc),
                rating=initial_rating or WalletRating.D_TIER
            )

            # Get historical data
            await self._analyze_wallet_history(metrics)

            # Start real-time monitoring
            task = asyncio.create_task(self._monitor_wallet_activity(wallet_address))

            self.tracked_wallets[wallet_address] = metrics
            self.trade_history[wallet_address] = []
            self.subscription_tasks[wallet_address] = task

            self.logger.info(
                f"Wallet tracking started: {wallet_address} "
                f"(Rating: {metrics.rating.value}, Win Rate: {metrics.win_rate_pct:.1f}%)"
            )

            return True

        except Exception as e:
            self.logger.error(f"Failed to add wallet tracking for {wallet_address}: {e}")
            return False

    async def remove_wallet_from_tracking(self, wallet_address: str) -> bool:
        """Remove wallet from tracking"""
        try:
            if wallet_address not in self.tracked_wallets:
                return False

            # Cancel monitoring task
            if wallet_address in self.subscription_tasks:
                self.subscription_tasks[wallet_address].cancel()
                del self.subscription_tasks[wallet_address]

            # Remove from tracking
            del self.tracked_wallets[wallet_address]
            if wallet_address in self.trade_history:
                del self.trade_history[wallet_address]

            self.logger.info(f"Stopped tracking wallet: {wallet_address}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to remove wallet tracking for {wallet_address}: {e}")
            return False

    async def _analyze_wallet_history(self, metrics: WalletMetrics, days: int = 30):
        """Analyze historical wallet performance"""
        try:
            # Get transaction history
            transactions = await self._get_wallet_transactions(metrics.address, days)

            if not transactions:
                self.logger.debug(f"No transaction history found for {metrics.address}")
                return

            # Parse and analyze trades
            trades = self._parse_trades_from_transactions(transactions)

            if not trades:
                return

            # Calculate performance metrics
            metrics.total_trades = len(trades)
            metrics.winning_trades = sum(1 for t in trades if t.get('pnl', 0) > 0)
            metrics.losing_trades = metrics.total_trades - metrics.winning_trades

            if metrics.total_trades > 0:
                metrics.win_rate_pct = (metrics.winning_trades / metrics.total_trades) * 100

            # Financial metrics
            pnls = [t.get('pnl', 0) for t in trades]
            metrics.total_pnl_sol = sum(pnls)

            if metrics.winning_trades > 0:
                winning_returns = [t.get('return_pct', 0) for t in trades if t.get('pnl', 0) > 0]
                metrics.avg_return_pct = np.mean(winning_returns)

            # Risk metrics
            position_sizes = [t.get('size_sol', 0) for t in trades]
            if position_sizes:
                metrics.avg_position_size_sol = np.mean(position_sizes)
                metrics.max_position_size_sol = max(position_sizes)

            # Calculate portfolio diversity
            tokens = set(t.get('token', '') for t in trades)
            metrics.portfolio_diversity_score = min(1.0, len(tokens) / 10)  # Normalize to 0-1

            # Calculate risk score
            metrics.risk_score = self._calculate_risk_score(trades, metrics)

            # Behavioral analysis
            hold_times = [t.get('hold_time_hours', 0) for t in trades if t.get('hold_time_hours', 0) > 0]
            if hold_times:
                metrics.avg_hold_time_hours = np.mean(hold_times)

            # Trading frequency
            if transactions:
                time_span_days = (datetime.now(timezone.utc) - 
                                transactions[0].get('timestamp', datetime.now(timezone.utc))).days
                if time_span_days > 0:
                    metrics.trading_frequency_per_day = metrics.total_trades / time_span_days

            # Determine rating
            metrics.rating = self._calculate_wallet_rating(metrics)

            # Calculate confidence
            metrics.confidence_score = self._calculate_confidence_score(metrics, len(transactions))

            self.logger.debug(
                f"Historical analysis completed for {metrics.address}: "
                f"{metrics.total_trades} trades, {metrics.win_rate_pct:.1f}% win rate"
            )

        except Exception as e:
            self.logger.error(f"Historical analysis failed for {metrics.address}: {e}")

    async def _get_wallet_transactions(self, wallet_address: str, days: int) -> List[Dict[str, Any]]:
        """Get wallet transaction history"""
        try:
            # This would integrate with Solana RPC or indexing services
            # For now, return simulated transaction data

            import random
            transactions = []

            for i in range(random.randint(5, 50)):  # Random number of transactions
                tx_time = datetime.now(timezone.utc) - timedelta(days=random.randint(0, days))

                transactions.append({
                    'signature': f'tx_{i}_{wallet_address[:8]}',
                    'timestamp': tx_time,
                    'type': random.choice(['swap', 'transfer']),
                    'success': random.choice([True, True, True, False]),  # 75% success rate
                    'tokens_involved': random.choice(['SOL->TOKEN', 'TOKEN->SOL']),
                    'amount_sol': random.uniform(0.01, 5.0)
                })

            return sorted(transactions, key=lambda x: x['timestamp'])

        except Exception as e:
            self.logger.error(f"Failed to get transactions for {wallet_address}: {e}")
            return []

    def _parse_trades_from_transactions(self, transactions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Parse individual trades from transaction history"""
        trades = []

        try:
            # Group transactions into trades (buy + sell pairs)
            # This is a simplified implementation

            for tx in transactions:
                if not tx.get('success', False):
                    continue

                # Simulate trade data
                import random
                trade = {
                    'timestamp': tx['timestamp'],
                    'token': f"Token{random.randint(1, 100)}",
                    'size_sol': tx['amount_sol'],
                    'pnl': random.uniform(-1.0, 3.0),  # Random PnL
                    'return_pct': random.uniform(-50, 200),  # -50% to +200%
                    'hold_time_hours': random.uniform(0.1, 168),  # Up to 1 week
                    'tx_signature': tx['signature']
                }

                trades.append(trade)

            return trades

        except Exception as e:
            self.logger.error(f"Failed to parse trades: {e}")
            return []

    def _calculate_risk_score(self, trades: List[Dict[str, Any]], metrics: WalletMetrics) -> float:
        """Calculate wallet risk score (0-1, higher = riskier)"""
        try:
            risk_factors = []

            # Position size variance
            position_sizes = [t.get('size_sol', 0) for t in trades]
            if position_sizes:
                size_variance = np.var(position_sizes) / np.mean(position_sizes) if np.mean(position_sizes) > 0 else 0
                risk_factors.append(min(1.0, size_variance))

            # Drawdown risk
            if metrics.max_drawdown_pct > 30:
                risk_factors.append(0.8)
            elif metrics.max_drawdown_pct > 15:
                risk_factors.append(0.5)
            else:
                risk_factors.append(0.2)

            # Win rate stability
            if metrics.win_rate_pct < 40:
                risk_factors.append(0.9)
            elif metrics.win_rate_pct < 60:
                risk_factors.append(0.4)
            else:
                risk_factors.append(0.1)

            # Portfolio concentration
            diversity_risk = 1.0 - metrics.portfolio_diversity_score
            risk_factors.append(diversity_risk)

            # Calculate weighted average
            return np.mean(risk_factors) if risk_factors else 0.5

        except Exception as e:
            self.logger.error(f"Risk score calculation failed: {e}")
            return 0.5

    def _calculate_wallet_rating(self, metrics: WalletMetrics) -> WalletRating:
        """Calculate wallet rating based on performance"""
        try:
            # Primary factor: win rate
            win_rate = metrics.win_rate_pct

            # Secondary factors
            risk_adjustment = 1.0 - metrics.risk_score
            volume_factor = min(1.0, metrics.total_volume_sol / 100)  # Normalize to reasonable volume
            trade_count_factor = min(1.0, metrics.total_trades / 50)  # Normalize to reasonable count

            # Composite score
            composite_score = (
                win_rate * 0.5 +  # 50% weight on win rate
                risk_adjustment * 100 * 0.3 +  # 30% weight on risk-adjusted performance  
                volume_factor * 100 * 0.1 +  # 10% weight on volume
                trade_count_factor * 100 * 0.1  # 10% weight on experience
            )

            # Determine rating
            if composite_score >= 85:
                return WalletRating.S_TIER
            elif composite_score >= 75:
                return WalletRating.A_TIER
            elif composite_score >= 60:
                return WalletRating.B_TIER
            elif composite_score >= 45:
                return WalletRating.C_TIER
            else:
                return WalletRating.D_TIER

        except Exception as e:
            self.logger.error(f"Rating calculation failed: {e}")
            return WalletRating.D_TIER

    def _calculate_confidence_score(self, metrics: WalletMetrics, tx_count: int) -> float:
        """Calculate confidence in wallet analysis"""
        try:
            confidence_factors = []

            # Data completeness
            if metrics.total_trades >= 20:
                confidence_factors.append(1.0)
            elif metrics.total_trades >= 10:
                confidence_factors.append(0.7)
            elif metrics.total_trades >= 5:
                confidence_factors.append(0.4)
            else:
                confidence_factors.append(0.1)

            # Time span
            time_span_days = (metrics.last_activity - metrics.first_seen).days
            if time_span_days >= 30:
                confidence_factors.append(1.0)
            elif time_span_days >= 14:
                confidence_factors.append(0.7)
            elif time_span_days >= 7:
                confidence_factors.append(0.4)
            else:
                confidence_factors.append(0.1)

            # Transaction volume
            if tx_count >= 100:
                confidence_factors.append(1.0)
            elif tx_count >= 50:
                confidence_factors.append(0.8)
            elif tx_count >= 20:
                confidence_factors.append(0.5)
            else:
                confidence_factors.append(0.2)

            return np.mean(confidence_factors)

        except Exception as e:
            self.logger.error(f"Confidence calculation failed: {e}")
            return 0.0

    async def _monitor_wallet_activity(self, wallet_address: str):
        """Monitor wallet for real-time trading activity"""
        try:
            self.logger.info(f"Starting real-time monitoring for {wallet_address}")

            while True:
                # Check for new transactions
                recent_transactions = await self._get_recent_transactions(wallet_address)

                for tx in recent_transactions:
                    await self._process_new_transaction(wallet_address, tx)

                # Wait before next check
                await asyncio.sleep(30)  # Check every 30 seconds

        except asyncio.CancelledError:
            self.logger.info(f"Monitoring cancelled for {wallet_address}")
        except Exception as e:
            self.logger.error(f"Monitoring failed for {wallet_address}: {e}")

            # Restart monitoring after delay
            await asyncio.sleep(60)
            await self._monitor_wallet_activity(wallet_address)

    async def _get_recent_transactions(self, wallet_address: str, 
                                     since_minutes: int = 5) -> List[Dict[str, Any]]:
        """Get recent transactions for wallet"""
        try:
            # This would query RPC or indexing services for recent transactions
            # For now, occasionally return simulated new transactions

            import random
            if random.random() < 0.1:  # 10% chance of new transaction
                return [{
                    'signature': f'new_tx_{wallet_address[:8]}_{int(datetime.now().timestamp())}',
                    'timestamp': datetime.now(timezone.utc),
                    'type': 'swap',
                    'success': True,
                    'amount_sol': random.uniform(0.01, 2.0),
                    'token_mint': f'Token{random.randint(1, 100)}'
                }]

            return []

        except Exception as e:
            self.logger.error(f"Failed to get recent transactions for {wallet_address}: {e}")
            return []

    async def _process_new_transaction(self, wallet_address: str, tx_data: Dict[str, Any]):
        """Process new transaction from tracked wallet"""
        try:
            # Parse transaction into trade event
            trade_event = TradeEvent(
                wallet_address=wallet_address,
                timestamp=tx_data['timestamp'],
                token_mint=tx_data.get('token_mint', ''),
                token_symbol=f"SYM{tx_data.get('token_mint', '')[:4]}",
                side='buy',  # Simplified
                amount_sol=tx_data['amount_sol'],
                amount_tokens=tx_data['amount_sol'] * 1000,  # Simplified
                price_per_token=tx_data['amount_sol'] / (tx_data['amount_sol'] * 1000),
                tx_signature=tx_data['signature'],
                success=tx_data.get('success', False),
                gas_fee_sol=0.0001
            )

            # Store trade event
            if wallet_address not in self.trade_history:
                self.trade_history[wallet_address] = []

            self.trade_history[wallet_address].append(trade_event)

            # Update metrics
            if wallet_address in self.tracked_wallets:
                metrics = self.tracked_wallets[wallet_address]
                metrics.last_activity = trade_event.timestamp
                metrics.total_trades += 1

            self.logger.info(
                f"New trade detected from {wallet_address}: "
                f"{trade_event.side} {trade_event.amount_sol:.3f} SOL of {trade_event.token_symbol}"
            )

            # Trigger copy trading decision
            await self._trigger_copy_trade_evaluation(wallet_address, trade_event)

        except Exception as e:
            self.logger.error(f"Failed to process transaction for {wallet_address}: {e}")

    async def _trigger_copy_trade_evaluation(self, wallet_address: str, trade_event: TradeEvent):
        """Trigger copy trading evaluation for new trade"""
        try:
            # This would notify the copy trading engine
            # For now, just log the event
            self.logger.debug(f"Copy trade evaluation triggered for {wallet_address} trade")

        except Exception as e:
            self.logger.error(f"Copy trade evaluation failed: {e}")

    def get_tracked_wallets(self) -> Dict[str, WalletMetrics]:
        """Get all tracked wallets and their metrics"""
        return self.tracked_wallets.copy()

    def get_wallet_trade_history(self, wallet_address: str, limit: int = 100) -> List[TradeEvent]:
        """Get trade history for specific wallet"""
        if wallet_address not in self.trade_history:
            return []

        return self.trade_history[wallet_address][-limit:]

    async def close(self):
        """Clean up resources"""
        # Cancel all monitoring tasks
        for task in self.subscription_tasks.values():
            task.cancel()

        # Wait for tasks to complete
        await asyncio.gather(*self.subscription_tasks.values(), return_exceptions=True)

        await self.http_client.aclose()

        self.logger.info("Wallet tracker closed")

class CopyTradingEngine:
    """
    Advanced copy trading execution engine

    Makes intelligent decisions about which trades to copy based on
    wallet performance, risk assessment, and current market conditions.
    """

    def __init__(self, wallet_tracker: WalletTracker, our_wallet_balance: float):
        self.wallet_tracker = wallet_tracker
        self.our_balance_sol = our_wallet_balance

        # Configuration
        self.min_wallet_rating = WalletRating.C_TIER  # Minimum rating to copy
        self.min_confidence = 0.5  # Minimum confidence to copy
        self.max_position_size_pct = 0.2  # Max 20% of balance per trade
        self.max_daily_risk_pct = 0.1  # Max 10% daily risk

        # Strategy settings
        self.copy_strategy = CopyStrategy.SCALED
        self.position_scaling_factor = 0.5  # Scale positions to 50% of original

        # Track our copy trades
        self.our_copy_trades: List[Dict[str, Any]] = []
        self.daily_risk_used = 0.0

        # Initialize logger
        from .logutil import get_logger
        self.logger = get_logger("copy_engine")

        self.logger.info("Copy trading engine initialized")

    async def evaluate_copy_trade(self, wallet_address: str, 
                                 trade_event: TradeEvent) -> CopyTradeDecision:
        """
        Evaluate whether to copy a trade from tracked wallet

        Args:
            wallet_address: Address of wallet that made the trade
            trade_event: The trade event to potentially copy

        Returns:
            CopyTradeDecision with recommendation
        """
        try:
            # Get wallet metrics
            if wallet_address not in self.wallet_tracker.tracked_wallets:
                return CopyTradeDecision(
                    should_copy=False,
                    reason="Wallet not tracked",
                    confidence=0.0,
                    suggested_position_size_sol=0.0,
                    max_slippage_bps=100,
                    urgency_level='low',
                    risk_factors=["Unknown wallet"]
                )

            wallet_metrics = self.wallet_tracker.tracked_wallets[wallet_address]
            risk_factors = []

            # Check wallet rating
            if wallet_metrics.rating.value not in [r.value for r in [WalletRating.S_TIER, WalletRating.A_TIER, WalletRating.B_TIER]]:
                return CopyTradeDecision(
                    should_copy=False,
                    reason=f"Wallet rating too low: {wallet_metrics.rating.value}",
                    confidence=0.0,
                    suggested_position_size_sol=0.0,
                    max_slippage_bps=100,
                    urgency_level='low',
                    risk_factors=["Low wallet rating"]
                )

            # Check confidence
            if wallet_metrics.confidence_score < self.min_confidence:
                return CopyTradeDecision(
                    should_copy=False,
                    reason=f"Confidence too low: {wallet_metrics.confidence_score:.2f}",
                    confidence=0.0,
                    suggested_position_size_sol=0.0,
                    max_slippage_bps=100,
                    urgency_level='low',
                    risk_factors=["Low confidence in analysis"]
                )

            # Check trade size reasonableness
            if trade_event.amount_sol < 0.01:
                return CopyTradeDecision(
                    should_copy=False,
                    reason="Trade size too small",
                    confidence=0.0,
                    suggested_position_size_sol=0.0,
                    max_slippage_bps=100,
                    urgency_level='low',
                    risk_factors=["Micro trade"]
                )

            if trade_event.amount_sol > wallet_metrics.max_position_size_sol * 3:
                risk_factors.append("Unusually large position for this wallet")

            # Check our daily risk limits
            if self.daily_risk_used >= self.max_daily_risk_pct:
                return CopyTradeDecision(
                    should_copy=False,
                    reason="Daily risk limit reached",
                    confidence=wallet_metrics.confidence_score,
                    suggested_position_size_sol=0.0,
                    max_slippage_bps=100,
                    urgency_level='low',
                    risk_factors=["Daily risk limit exceeded"]
                )

            # Calculate position size
            suggested_size = self._calculate_position_size(
                wallet_metrics, trade_event, self.our_balance_sol
            )

            if suggested_size == 0:
                return CopyTradeDecision(
                    should_copy=False,
                    reason="Position size calculation returned zero",
                    confidence=wallet_metrics.confidence_score,
                    suggested_position_size_sol=0.0,
                    max_slippage_bps=100,
                    urgency_level='low',
                    risk_factors=["Position sizing failed"]
                )

            # Determine urgency based on wallet performance and trade characteristics
            urgency_level = self._determine_urgency_level(wallet_metrics, trade_event)

            # Calculate appropriate slippage tolerance
            max_slippage_bps = self._calculate_slippage_tolerance(wallet_metrics, trade_event)

            # Final confidence calculation
            final_confidence = self._calculate_copy_confidence(
                wallet_metrics, trade_event, risk_factors
            )

            self.logger.info(
                f"Copy trade evaluation for {wallet_address}: "
                f"COPY={final_confidence > 0.6}, size={suggested_size:.3f} SOL, "
                f"confidence={final_confidence:.2f}"
            )

            return CopyTradeDecision(
                should_copy=final_confidence > 0.6,
                reason=f"Wallet rating: {wallet_metrics.rating.value}, confidence: {final_confidence:.2f}",
                confidence=final_confidence,
                suggested_position_size_sol=suggested_size,
                max_slippage_bps=max_slippage_bps,
                urgency_level=urgency_level,
                risk_factors=risk_factors
            )

        except Exception as e:
            self.logger.error(f"Copy trade evaluation failed: {e}")
            return CopyTradeDecision(
                should_copy=False,
                reason=f"Evaluation error: {str(e)}",
                confidence=0.0,
                suggested_position_size_sol=0.0,
                max_slippage_bps=100,
                urgency_level='low',
                risk_factors=["System error"]
            )

    def _calculate_position_size(self, wallet_metrics: WalletMetrics, 
                               trade_event: TradeEvent, our_balance: float) -> float:
        """Calculate appropriate position size for copy trade"""
        try:
            # Base position size based on strategy
            if self.copy_strategy == CopyStrategy.MIRROR:
                # Copy exact size (if we have enough balance)
                base_size = min(trade_event.amount_sol, our_balance * self.max_position_size_pct)

            elif self.copy_strategy == CopyStrategy.SCALED:
                # Scale position based on our balance vs their typical size
                scale_factor = our_balance / max(wallet_metrics.avg_position_size_sol * 10, 1.0)  # Assume they have 10x our balance
                base_size = trade_event.amount_sol * min(scale_factor, 1.0) * self.position_scaling_factor

            else:  # SELECTIVE or DELAYED
                # Conservative sizing for selective copying
                base_size = min(trade_event.amount_sol * 0.3, our_balance * 0.1)

            # Adjust for wallet performance
            performance_multiplier = 1.0
            if wallet_metrics.rating == WalletRating.S_TIER:
                performance_multiplier = 1.5
            elif wallet_metrics.rating == WalletRating.A_TIER:
                performance_multiplier = 1.2
            elif wallet_metrics.rating == WalletRating.B_TIER:
                performance_multiplier = 1.0
            else:
                performance_multiplier = 0.5

            # Adjust for confidence
            confidence_multiplier = wallet_metrics.confidence_score

            # Final position size
            final_size = base_size * performance_multiplier * confidence_multiplier

            # Ensure within limits
            final_size = min(final_size, our_balance * self.max_position_size_pct)
            final_size = max(final_size, 0.01)  # Minimum viable size

            return round(final_size, 6)  # Round to 6 decimal places

        except Exception as e:
            self.logger.error(f"Position size calculation failed: {e}")
            return 0.0

    def _determine_urgency_level(self, wallet_metrics: WalletMetrics, 
                               trade_event: TradeEvent) -> str:
        """Determine urgency level for copy trade execution"""
        try:
            urgency_score = 0

            # Wallet rating influence
            if wallet_metrics.rating == WalletRating.S_TIER:
                urgency_score += 3
            elif wallet_metrics.rating == WalletRating.A_TIER:
                urgency_score += 2
            elif wallet_metrics.rating == WalletRating.B_TIER:
                urgency_score += 1

            # Trade size relative to wallet average
            if trade_event.amount_sol > wallet_metrics.avg_position_size_sol * 2:
                urgency_score += 2  # Large position suggests high conviction

            # Recent performance
            if wallet_metrics.win_rate_pct > 80:
                urgency_score += 1

            # Confidence level
            if wallet_metrics.confidence_score > 0.8:
                urgency_score += 1

            # Determine urgency level
            if urgency_score >= 6:
                return 'critical'
            elif urgency_score >= 4:
                return 'high'
            elif urgency_score >= 2:
                return 'medium'
            else:
                return 'low'

        except Exception as e:
            self.logger.error(f"Urgency calculation failed: {e}")
            return 'medium'

    def _calculate_slippage_tolerance(self, wallet_metrics: WalletMetrics,
                                    trade_event: TradeEvent) -> int:
        """Calculate appropriate slippage tolerance in basis points"""
        try:
            base_slippage = 50  # 0.5% base slippage

            # Adjust for wallet quality
            if wallet_metrics.rating == WalletRating.S_TIER:
                base_slippage = 100  # Allow more slippage for top performers
            elif wallet_metrics.rating == WalletRating.A_TIER:
                base_slippage = 75

            # Adjust for trade size (larger trades need more slippage tolerance)
            if trade_event.amount_sol > 1.0:
                base_slippage += 25
            elif trade_event.amount_sol > 0.5:
                base_slippage += 15

            return min(base_slippage, 200)  # Cap at 2%

        except Exception as e:
            self.logger.error(f"Slippage calculation failed: {e}")
            return 100  # Default 1%

    def _calculate_copy_confidence(self, wallet_metrics: WalletMetrics,
                                 trade_event: TradeEvent, risk_factors: List[str]) -> float:
        """Calculate final confidence for copy trading decision"""
        try:
            confidence_factors = []

            # Wallet performance confidence
            performance_conf = min(1.0, wallet_metrics.win_rate_pct / 100 + 0.2)  # Boost confidence slightly
            confidence_factors.append(performance_conf)

            # Analysis confidence
            confidence_factors.append(wallet_metrics.confidence_score)

            # Rating confidence  
            rating_conf = {
                WalletRating.S_TIER: 1.0,
                WalletRating.A_TIER: 0.8,
                WalletRating.B_TIER: 0.6,
                WalletRating.C_TIER: 0.4,
                WalletRating.D_TIER: 0.1
            }.get(wallet_metrics.rating, 0.1)
            confidence_factors.append(rating_conf)

            # Trade characteristics confidence
            trade_conf = 0.7  # Base confidence for any trade
            if trade_event.amount_sol >= wallet_metrics.avg_position_size_sol:
                trade_conf += 0.2  # Higher confidence for typical-sized trades
            confidence_factors.append(trade_conf)

            # Risk adjustment
            risk_penalty = len(risk_factors) * 0.1

            final_confidence = np.mean(confidence_factors) - risk_penalty

            return max(0.0, min(1.0, final_confidence))

        except Exception as e:
            self.logger.error(f"Copy confidence calculation failed: {e}")
            return 0.0

    async def execute_copy_trade(self, decision: CopyTradeDecision, 
                               trade_event: TradeEvent) -> Dict[str, Any]:
        """
        Execute copy trade based on decision

        Args:
            decision: Copy trade decision
            trade_event: Original trade event to copy

        Returns:
            Execution result
        """
        try:
            if not decision.should_copy:
                return {
                    'success': False,
                    'reason': 'Decision was not to copy',
                    'decision_reason': decision.reason
                }

            self.logger.info(
                f"Executing copy trade: {trade_event.side} {decision.suggested_position_size_sol:.3f} SOL "
                f"of {trade_event.token_symbol} (urgency: {decision.urgency_level})"
            )

            # This would integrate with the actual trading execution system
            # For now, simulate the execution

            import random
            execution_success = random.choice([True, True, True, False])  # 75% success rate

            if execution_success:
                # Record successful copy trade
                copy_trade_record = {
                    'timestamp': datetime.now(timezone.utc),
                    'original_wallet': trade_event.wallet_address,
                    'original_tx': trade_event.tx_signature,
                    'our_tx': f'copy_tx_{int(datetime.now().timestamp())}',
                    'token_mint': trade_event.token_mint,
                    'token_symbol': trade_event.token_symbol,
                    'side': trade_event.side,
                    'amount_sol': decision.suggested_position_size_sol,
                    'confidence': decision.confidence,
                    'urgency': decision.urgency_level,
                    'success': True
                }

                self.our_copy_trades.append(copy_trade_record)

                # Update daily risk usage
                self.daily_risk_used += decision.suggested_position_size_sol / self.our_balance_sol

                self.logger.info(
                    f"Copy trade executed successfully: {copy_trade_record['our_tx']} "
                    f"({decision.suggested_position_size_sol:.3f} SOL)"
                )

                return {
                    'success': True,
                    'tx_signature': copy_trade_record['our_tx'],
                    'amount_sol': decision.suggested_position_size_sol,
                    'trade_record': copy_trade_record
                }
            else:
                self.logger.warning("Copy trade execution failed")
                return {
                    'success': False,
                    'reason': 'Execution failed',
                    'error': 'Simulated execution failure'
                }

        except Exception as e:
            self.logger.error(f"Copy trade execution failed: {e}")
            return {
                'success': False,
                'reason': 'Execution error',
                'error': str(e)
            }

    def get_copy_trade_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get history of our copy trades"""
        return self.our_copy_trades[-limit:]

    def get_daily_performance(self) -> Dict[str, Any]:
        """Get today's copy trading performance"""
        today = datetime.now(timezone.utc).date()

        today_trades = [
            t for t in self.our_copy_trades 
            if t['timestamp'].date() == today
        ]

        return {
            'trades_today': len(today_trades),
            'successful_trades': sum(1 for t in today_trades if t['success']),
            'total_volume_sol': sum(t['amount_sol'] for t in today_trades),
            'risk_used_pct': self.daily_risk_used * 100,
            'average_confidence': np.mean([t['confidence'] for t in today_trades]) if today_trades else 0
        }

if __name__ == "__main__":
    # Test copy trading system
    async def test_copy_trading():
        print("🧪 Testing Copy Trading System...")

        try:
            from .config import get_config
            from .rpc import create_rpc_client

            config = get_config()

            async with create_rpc_client(config.solana.sol_rpc_url) as rpc_client:
                # Create wallet tracker
                tracker = WalletTracker(rpc_client)

                # Add test wallet
                test_wallet = "TestWallet111111111111111111111111111111111"
                success = await tracker.add_wallet_to_track(test_wallet)
                print(f"✅ Wallet tracking: {success}")

                # Create copy trading engine
                engine = CopyTradingEngine(tracker, our_wallet_balance=1.0)

                # Test trade evaluation
                test_trade = TradeEvent(
                    wallet_address=test_wallet,
                    timestamp=datetime.now(timezone.utc),
                    token_mint="TestToken111111111111111111111111111111111",
                    token_symbol="TEST",
                    side="buy",
                    amount_sol=0.1,
                    amount_tokens=100.0,
                    price_per_token=0.001,
                    tx_signature="test_tx_123",
                    success=True,
                    gas_fee_sol=0.0001
                )

                decision = await engine.evaluate_copy_trade(test_wallet, test_trade)
                print(f"✅ Copy decision: {decision.should_copy} ({decision.reason})")

                # Test execution
                if decision.should_copy:
                    result = await engine.execute_copy_trade(decision, test_trade)
                    print(f"✅ Copy execution: {result['success']}")

                await tracker.close()

        except Exception as e:
            print(f"❌ Copy trading test failed: {e}")

    # Run the test
    asyncio.run(test_copy_trading())
