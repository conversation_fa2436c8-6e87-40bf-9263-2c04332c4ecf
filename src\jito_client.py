"""
Advanced Jito MEV Protection Client

This module provides comprehensive MEV protection through Jito bundle integration,
priority fee optimization, and sandwich attack prevention for Solana trading.
"""
import asyncio
import json
import base64
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from decimal import Decimal
import httpx
from solders.transaction import VersionedTransaction
from solders.keypair import Keypair
from tenacity import retry, stop_after_attempt, wait_exponential



@dataclass
class JitoTipAccount:
    """Jito tip account information"""
    address: str
    name: str
    commission_bps: int

@dataclass
class Bundle:
    """Transaction bundle for atomic execution"""
    transactions: List[VersionedTransaction]
    tip_lamports: int
    max_tip_lamports: Optional[int] = None

    def __post_init__(self):
        if self.max_tip_lamports is None:
            self.max_tip_lamports = self.tip_lamports * 2

@dataclass
class BundleResult:
    """Bundle execution result"""
    bundle_id: str
    status: str  # 'pending', 'landed', 'failed'
    signature: Optional[str] = None
    slot: Optional[int] = None
    error: Optional[str] = None

class NetworkCongestionMonitor:
    """Monitor network congestion for dynamic fee adjustment"""

    def __init__(self, rpc_client):
        self.rpc_client = rpc_client
        self.recent_fees = []
        self.congestion_history = []

        # Initialize logger
        from .logutil import get_logger
        self.logger = get_logger("congestion")

    async def get_current_congestion_level(self) -> float:
        """Get current network congestion level (0.0 to 1.0)"""
        try:
            # Get recent block information
            recent_blockhash = await self.rpc_client.get_latest_blockhash()
            slot = recent_blockhash.get("context", {}).get("slot", 0)

            # Get fee information for recent slots
            fee_data = await self.get_recent_priority_fees()

            if not fee_data:
                return 0.5  # Default moderate congestion

            # Calculate congestion based on fee percentiles
            fees = [fee["prioritizationFee"] for fee in fee_data if fee["prioritizationFee"] > 0]

            if not fees:
                return 0.1  # Low congestion

            # Calculate congestion score
            avg_fee = sum(fees) / len(fees)
            max_fee = max(fees)

            # Normalize to 0.0-1.0 scale
            congestion_score = min(1.0, (avg_fee + max_fee * 0.3) / 100000)  # Scale based on lamports

            self.congestion_history.append(congestion_score)
            if len(self.congestion_history) > 100:
                self.congestion_history.pop(0)

            self.logger.debug(f"Network congestion: {congestion_score:.3f}")
            return congestion_score

        except Exception as e:
            self.logger.warning(f"Failed to get congestion level: {e}")
            return 0.5

    async def get_recent_priority_fees(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get recent priority fees from the network"""
        try:
            # This would call the RPC method to get recent priority fees
            # Implementation depends on RPC provider capabilities
            return []
        except Exception as e:
            self.logger.warning(f"Failed to get priority fees: {e}")
            return []

    def get_congestion_multiplier(self, congestion_level: float) -> float:
        """Get fee multiplier based on congestion level"""
        if congestion_level < 0.2:
            return 1.0  # Low congestion
        elif congestion_level < 0.5:
            return 1.5  # Moderate congestion
        elif congestion_level < 0.8:
            return 2.5  # High congestion
        else:
            return 4.0  # Extreme congestion

class PriorityFeeManager:
    """Advanced priority fee management with MEV protection"""

    def __init__(self, rpc_client):
        self.rpc_client = rpc_client
        self.congestion_monitor = NetworkCongestionMonitor(rpc_client)
        self.fee_history = []

        # Initialize logger
        from .logutil import get_logger
        self.logger = get_logger("priority_fees")

    async def get_optimal_priority_fee(self, urgency_level: str = "medium", 
                                     mev_protection: bool = True) -> int:
        """
        Calculate optimal priority fee with MEV protection

        Args:
            urgency_level: 'low', 'medium', 'high', 'critical'
            mev_protection: Whether to add MEV protection premium

        Returns:
            Priority fee in micro-lamports
        """
        try:
            # Get current network conditions
            congestion_level = await self.congestion_monitor.get_current_congestion_level()
            congestion_multiplier = self.congestion_monitor.get_congestion_multiplier(congestion_level)

            # Base fees by urgency level (micro-lamports)
            base_fees = {
                'low': 1000,
                'medium': 5000,
                'high': 15000,
                'critical': 50000
            }

            base_fee = base_fees.get(urgency_level, 5000)

            # Apply congestion multiplier
            adjusted_fee = int(base_fee * congestion_multiplier)

            # Add MEV protection premium
            if mev_protection:
                mev_premium = max(10000, int(adjusted_fee * 0.5))  # 50% premium or 10k minimum
                adjusted_fee += mev_premium
                self.logger.debug(f"Added MEV protection premium: {mev_premium}")

            # Apply urgency multiplier
            urgency_multipliers = {
                'low': 1.0,
                'medium': 1.2,
                'high': 2.0,
                'critical': 4.0
            }

            final_fee = int(adjusted_fee * urgency_multipliers.get(urgency_level, 1.2))

            # Cap maximum fee for safety
            max_fee = 500000  # 0.5 SOL in micro-lamports
            final_fee = min(final_fee, max_fee)

            self.logger.info(
                f"Priority fee calculated: {final_fee} micro-lamports "
                f"(urgency: {urgency_level}, congestion: {congestion_level:.3f})"
            )

            return final_fee

        except Exception as e:
            self.logger.error(f"Priority fee calculation failed: {e}")
            return 10000  # Safe default

class JitoClient:
    """
    Advanced Jito MEV protection client

    Provides bundle transaction capabilities for atomic execution
    and protection against sandwich attacks and MEV extraction.
    """

    def __init__(self, rpc_client, wallet_keypair: Keypair):
        self.rpc_client = rpc_client
        self.wallet_keypair = wallet_keypair
        self.priority_fee_manager = PriorityFeeManager(rpc_client)

        # Jito endpoints
        self.jito_rpc_url = "https://mainnet.block-engine.jito.wtf/api/v1"
        self.jito_bundle_url = f"{self.jito_rpc_url}/bundles"

        # HTTP client for Jito API
        self.http_client = httpx.AsyncClient(
            timeout=httpx.Timeout(30.0),
            limits=httpx.Limits(max_keepalive_connections=5)
        )

        # Tip accounts (official Jito tip accounts)
        self.tip_accounts = [
            JitoTipAccount("96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5", "jito_tip_1", 0),
            JitoTipAccount("HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe", "jito_tip_2", 0),
            JitoTipAccount("Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY", "jito_tip_3", 0),
            JitoTipAccount("ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49", "jito_tip_4", 0),
            JitoTipAccount("DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh", "jito_tip_5", 0),
        ]

        # Initialize logger
        from .logutil import get_logger
        self.logger = get_logger("jito_client")

        self.logger.info("Jito MEV protection client initialized")

    async def __aenter__(self):
        """Async context manager entry"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()

    async def close(self):
        """Close HTTP client"""
        await self.http_client.aclose()

    def select_optimal_tip_account(self) -> JitoTipAccount:
        """Select optimal tip account based on current conditions"""
        # For now, rotate through tip accounts
        # Could be enhanced with performance tracking
        import random
        return random.choice(self.tip_accounts)

    async def calculate_optimal_tip(self, priority_level: str = "medium") -> int:
        """
        Calculate optimal tip amount based on network conditions

        Args:
            priority_level: 'low', 'medium', 'high', 'critical'

        Returns:
            Tip amount in lamports
        """
        try:
            # Get network congestion level
            congestion_level = await self.priority_fee_manager.congestion_monitor.get_current_congestion_level()

            # Base tip amounts by priority level (lamports)
            base_tips = {
                'low': 10000,      # 0.00001 SOL
                'medium': 50000,   # 0.00005 SOL  
                'high': 100000,    # 0.0001 SOL
                'critical': 500000 # 0.0005 SOL
            }

            base_tip = base_tips.get(priority_level, 50000)

            # Adjust for network congestion
            congestion_multiplier = self.priority_fee_manager.congestion_monitor.get_congestion_multiplier(congestion_level)

            optimal_tip = int(base_tip * congestion_multiplier)

            # Cap maximum tip for safety
            max_tip = 5000000  # 0.005 SOL
            optimal_tip = min(optimal_tip, max_tip)

            self.logger.debug(
                f"Optimal tip calculated: {optimal_tip} lamports "
                f"(priority: {priority_level}, congestion: {congestion_level:.3f})"
            )

            return optimal_tip

        except Exception as e:
            self.logger.error(f"Tip calculation failed: {e}")
            return 50000  # Safe default

    def create_tip_transaction(self, tip_account: JitoTipAccount, tip_lamports: int) -> VersionedTransaction:
        """Create tip transaction for bundle inclusion"""
        try:
            from solana.system_program import transfer, TransferParams
            from solana.transaction import Transaction
            from solana.publickey import PublicKey

            # Create tip instruction
            tip_instruction = transfer(
                TransferParams(
                    from_pubkey=self.wallet_keypair.public_key,
                    to_pubkey=PublicKey(tip_account.address),
                    lamports=tip_lamports
                )
            )

            # Create transaction
            transaction = Transaction()
            transaction.add(tip_instruction)

            # Convert to versioned transaction (simplified - would need proper implementation)
            # This is a placeholder - actual implementation would need proper version handling
            versioned_tx = VersionedTransaction.legacy(transaction)

            self.logger.debug(f"Created tip transaction: {tip_lamports} lamports to {tip_account.name}")

            return versioned_tx

        except Exception as e:
            self.logger.error(f"Failed to create tip transaction: {e}")
            raise

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=8)
    )
    async def send_bundle(self, transactions: List[VersionedTransaction], 
                         tip_lamports: Optional[int] = None,
                         priority_level: str = "medium") -> BundleResult:
        """
        Send bundle of transactions with MEV protection

        Args:
            transactions: List of signed transactions to bundle
            tip_lamports: Tip amount (calculated automatically if not provided)
            priority_level: Bundle priority level

        Returns:
            BundleResult with execution status
        """
        try:
            # Calculate optimal tip if not provided
            if tip_lamports is None:
                tip_lamports = await self.calculate_optimal_tip(priority_level)

            # Select tip account
            tip_account = self.select_optimal_tip_account()

            # Create tip transaction
            tip_tx = self.create_tip_transaction(tip_account, tip_lamports)

            # Combine all transactions
            bundle_transactions = list(transactions) + [tip_tx]

            # Sign all transactions
            for tx in bundle_transactions:
                tx.sign([self.wallet_keypair])

            # Prepare bundle payload
            bundle_payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "sendBundle",
                "params": [
                    [base64.b64encode(tx.serialize()).decode() for tx in bundle_transactions]
                ]
            }

            self.logger.info(
                f"Sending bundle with {len(transactions)} transactions + tip "
                f"(tip: {tip_lamports} lamports, account: {tip_account.name})"
            )

            # Send bundle to Jito
            response = await self.http_client.post(
                self.jito_bundle_url,
                json=bundle_payload,
                headers={"Content-Type": "application/json"}
            )

            response.raise_for_status()
            result = response.json()

            if "error" in result:
                error_msg = result["error"].get("message", "Unknown error")
                self.logger.error(f"Bundle submission error: {error_msg}")
                return BundleResult(
                    bundle_id="",
                    status="failed",
                    error=error_msg
                )

            bundle_id = result.get("result", "")

            self.logger.info(f"Bundle submitted successfully: {bundle_id}")

            return BundleResult(
                bundle_id=bundle_id,
                status="pending"
            )

        except Exception as e:
            self.logger.error(f"Bundle submission failed: {e}")
            return BundleResult(
                bundle_id="",
                status="failed", 
                error=str(e)
            )

    async def check_bundle_status(self, bundle_id: str) -> BundleResult:
        """Check the status of a submitted bundle"""
        try:
            # Query bundle status
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getBundleStatuses",
                "params": [[bundle_id]]
            }

            response = await self.http_client.post(
                self.jito_rpc_url,
                json=payload,
                headers={"Content-Type": "application/json"}
            )

            response.raise_for_status()
            result = response.json()

            if "error" in result:
                return BundleResult(bundle_id=bundle_id, status="failed", error=result["error"]["message"])

            bundle_statuses = result.get("result", {}).get("value", [])

            if not bundle_statuses:
                return BundleResult(bundle_id=bundle_id, status="unknown")

            status_info = bundle_statuses[0]
            status = status_info.get("confirmation_status", "unknown")

            return BundleResult(
                bundle_id=bundle_id,
                status=status,
                slot=status_info.get("slot"),
                signature=status_info.get("transactions", [{}])[0].get("signature") if status_info.get("transactions") else None
            )

        except Exception as e:
            self.logger.error(f"Bundle status check failed: {e}")
            return BundleResult(bundle_id=bundle_id, status="unknown", error=str(e))

    async def wait_for_bundle_confirmation(self, bundle_id: str, timeout_seconds: int = 60) -> BundleResult:
        """Wait for bundle confirmation with timeout"""
        start_time = asyncio.get_event_loop().time()

        while (asyncio.get_event_loop().time() - start_time) < timeout_seconds:
            result = await self.check_bundle_status(bundle_id)

            if result.status in ["landed", "failed"]:
                return result

            await asyncio.sleep(2)  # Check every 2 seconds

        # Timeout reached
        return BundleResult(bundle_id=bundle_id, status="timeout")

    async def execute_protected_swap(self, swap_transaction: VersionedTransaction,
                                   priority_level: str = "high") -> BundleResult:
        """
        Execute a single swap transaction with MEV protection

        Args:
            swap_transaction: The swap transaction to protect
            priority_level: Protection priority level

        Returns:
            BundleResult with execution status
        """
        try:
            self.logger.info("Executing MEV-protected swap")

            # Send as bundle with tip for MEV protection
            result = await self.send_bundle([swap_transaction], priority_level=priority_level)

            if result.status == "pending":
                # Wait for confirmation
                final_result = await self.wait_for_bundle_confirmation(result.bundle_id, timeout_seconds=45)

                if final_result.status == "landed":
                    self.logger.info(f"MEV-protected swap successful: {final_result.signature}")
                else:
                    self.logger.warning(f"MEV-protected swap status: {final_result.status}")

                return final_result

            return result

        except Exception as e:
            self.logger.error(f"MEV-protected swap failed: {e}")
            return BundleResult(bundle_id="", status="failed", error=str(e))

# Utility functions
async def create_jito_client(rpc_client, wallet_keypair: Keypair) -> JitoClient:
    """Create and return a configured Jito client"""
    client = JitoClient(rpc_client, wallet_keypair)
    return client

if __name__ == "__main__":
    # Test Jito client functionality
    async def test_jito_client():
        print("🧪 Testing Jito MEV protection client...")

        try:
            from .config import get_config
            from .solana_wallet import create_wallet_from_config
            from .rpc import create_rpc_client

            config = get_config()
            wallet = create_wallet_from_config(config)

            async with create_rpc_client(config.solana.sol_rpc_url) as rpc_client:
                async with create_jito_client(rpc_client, wallet._keypair) as jito_client:
                    # Test tip calculation
                    tip = await jito_client.calculate_optimal_tip("medium")
                    print(f"✅ Optimal tip calculated: {tip} lamports")

                    # Test priority fee calculation
                    priority_fee = await jito_client.priority_fee_manager.get_optimal_priority_fee("high", True)
                    print(f"✅ Priority fee calculated: {priority_fee} micro-lamports")

        except Exception as e:
            print(f"❌ Jito test failed: {e}")

    # Run the test
    asyncio.run(test_jito_client())
