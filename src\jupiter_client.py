"""
Solana Memecoin Sniper Bot - Jupiter Integration

This module provides Jupiter v6 API integration for optimal swap execution
with quote analysis, price impact validation, and transaction building.
"""
import asyncio
import base64
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from decimal import Decimal, ROUND_DOWN
import httpx
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from .logutil import get_logger

# Jupiter API endpoints
JUPITER_V6_BASE = "https://quote-api.jup.ag/v6"
JUPITER_QUOTE_URL = f"{JUPITER_V6_BASE}/quote"
JUPITER_SWAP_URL = f"{JUPITER_V6_BASE}/swap"

@dataclass
class JupiterQuote:
    """Jupiter quote response data structure"""
    input_mint: str
    output_mint: str
    input_amount: int
    output_amount: int
    other_amount_threshold: int
    swap_mode: str
    slippage_bps: int
    price_impact_pct: float
    platform_fee: Optional[Dict[str, Any]]
    route_plan: List[Dict[str, Any]]
    context_slot: int
    time_taken: float

    @classmethod
    def from_response(cls, data: Dict[str, Any]) -> 'JupiterQuote':
        """Create JupiterQuote from API response"""
        return cls(
            input_mint=data.get("inputMint", ""),
            output_mint=data.get("outputMint", ""),
            input_amount=int(data.get("inAmount", 0)),
            output_amount=int(data.get("outAmount", 0)),
            other_amount_threshold=int(data.get("otherAmountThreshold", 0)),
            swap_mode=data.get("swapMode", "ExactIn"),
            slippage_bps=int(data.get("slippageBps", 0)),
            price_impact_pct=float(data.get("priceImpactPct", 0.0)) * 100,  # Convert to percentage
            platform_fee=data.get("platformFee"),
            route_plan=data.get("routePlan", []),
            context_slot=int(data.get("contextSlot", 0)),
            time_taken=float(data.get("timeTaken", 0.0))
        )

    def get_route_description(self) -> str:
        """Get human-readable route description"""
        if not self.route_plan:
            return "Direct"

        route_steps = []
        for step in self.route_plan:
            swap_info = step.get("swapInfo", {})
            label = swap_info.get("label", "Unknown")
            route_steps.append(label)

        return " → ".join(route_steps)

    def calculate_price_per_token(self, input_decimals: int = 9, output_decimals: int = 9) -> float:
        """Calculate effective price per token"""
        if self.output_amount == 0:
            return 0.0

        input_tokens = self.input_amount / (10 ** input_decimals)
        output_tokens = self.output_amount / (10 ** output_decimals)

        return input_tokens / output_tokens

    def is_acceptable_impact(self, max_impact_pct: float) -> bool:
        """Check if price impact is within acceptable limits"""
        return self.price_impact_pct <= max_impact_pct

@dataclass
class SwapTransaction:
    """Swap transaction data structure"""
    swap_transaction: str  # Base64 encoded transaction
    last_valid_block_height: int
    prioritization_fee_lamports: int
    compute_unit_limit: int

    @classmethod
    def from_response(cls, data: Dict[str, Any]) -> 'SwapTransaction':
        """Create SwapTransaction from API response"""
        return cls(
            swap_transaction=data.get("swapTransaction", ""),
            last_valid_block_height=int(data.get("lastValidBlockHeight", 0)),
            prioritization_fee_lamports=int(data.get("prioritizationFeeLamports", 0)),
            compute_unit_limit=int(data.get("computeUnitLimit", 0))
        )

class JupiterError(Exception):
    """Custom exception for Jupiter API errors"""
    pass

class JupiterClient:
    """
    Jupiter v6 API client for swap quotes and transaction building

    This client provides high-level access to Jupiter's swap aggregator
    with built-in error handling, retry logic, and price impact validation.
    """

    def __init__(self, timeout: int = 10, max_retries: int = 3):
        """
        Initialize Jupiter client

        Args:
            timeout: Request timeout in seconds
            max_retries: Maximum retry attempts
        """
        self.timeout = timeout
        self.max_retries = max_retries

        # HTTP client with optimized settings for trading
        self._client = httpx.AsyncClient(
            timeout=httpx.Timeout(timeout),
            limits=httpx.Limits(max_keepalive_connections=5, max_connections=10),
            http2=True
        )

        # Initialize logger
        self.logger = get_logger("jupiter")

        self.logger.info("Jupiter client initialized")

    async def __aenter__(self):
        """Async context manager entry"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()

    async def close(self):
        """Close the HTTP client"""
        await self._client.aclose()
        self.logger.debug("Jupiter client closed")

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=0.5, min=0.5, max=4),
        retry=retry_if_exception_type((httpx.RequestError, httpx.HTTPStatusError))
    )
    async def get_quote(self, 
                       input_mint: str,
                       output_mint: str, 
                       amount: int,
                       slippage_bps: int = 50,
                       swap_mode: str = "ExactIn",
                       only_direct_routes: bool = False,
                       as_legacy_transaction: bool = False,
                       platform_fee_bps: Optional[int] = None,
                       max_accounts: Optional[int] = None) -> JupiterQuote:
        """
        Get swap quote from Jupiter

        Args:
            input_mint: Input token mint address
            output_mint: Output token mint address  
            amount: Input amount in smallest units (lamports/token units)
            slippage_bps: Slippage tolerance in basis points
            swap_mode: "ExactIn" or "ExactOut"
            only_direct_routes: Only return direct routes
            as_legacy_transaction: Return legacy transaction format
            platform_fee_bps: Platform fee in basis points
            max_accounts: Maximum accounts in transaction

        Returns:
            JupiterQuote object with route and pricing information

        Raises:
            JupiterError: If quote request fails
        """
        params = {
            "inputMint": input_mint,
            "outputMint": output_mint,
            "amount": str(amount),
            "slippageBps": str(slippage_bps),
            "swapMode": swap_mode,
            "onlyDirectRoutes": str(only_direct_routes).lower(),
            "asLegacyTransaction": str(as_legacy_transaction).lower()
        }

        # Add optional parameters
        if platform_fee_bps is not None:
            params["platformFeeBps"] = str(platform_fee_bps)
        if max_accounts is not None:
            params["maxAccounts"] = str(max_accounts)

        try:
            self.logger.debug(f"Requesting quote: {input_mint} → {output_mint}, amount: {amount}")

            response = await self._client.get(JUPITER_QUOTE_URL, params=params)
            response.raise_for_status()

            data = response.json()

            # Check for API errors
            if "error" in data:
                raise JupiterError(f"Jupiter API error: {data['error']}")

            if not data:
                raise JupiterError("Empty response from Jupiter API")

            quote = JupiterQuote.from_response(data)

            self.logger.info(
                f"Quote received: {quote.output_amount} tokens "
                f"(impact: {quote.price_impact_pct:.3f}%, route: {quote.get_route_description()})"
            )

            return quote

        except httpx.RequestError as e:
            self.logger.error(f"Jupiter quote request failed: {e}")
            raise JupiterError(f"Quote request failed: {e}")
        except httpx.HTTPStatusError as e:
            error_msg = f"Jupiter quote HTTP error: {e.response.status_code}"
            if e.response.status_code == 400:
                try:
                    error_data = e.response.json()
                    error_msg += f" - {error_data.get('error', 'Bad Request')}"
                except Exception:
                    pass
            self.logger.error(error_msg)
            raise JupiterError(error_msg)
        except Exception as e:
            self.logger.error(f"Jupiter quote processing error: {e}")
            raise JupiterError(f"Quote processing failed: {e}")

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=0.5, min=0.5, max=4),
        retry=retry_if_exception_type((httpx.RequestError, httpx.HTTPStatusError))
    )
    async def build_swap_transaction(self,
                                   user_public_key: str,
                                   quote: JupiterQuote,
                                   wrap_and_unwrap_sol: bool = True,
                                   use_shared_accounts: bool = True,
                                   as_legacy_transaction: bool = False,
                                   compute_unit_price_micro_lamports: int = 0,
                                   dynamic_compute_unit_limit: bool = True,
                                   priority_level: str = "medium") -> SwapTransaction:
        """
        Build swap transaction from quote

        Args:
            user_public_key: User's wallet public key
            quote: JupiterQuote from get_quote()
            wrap_and_unwrap_sol: Auto wrap/unwrap SOL
            use_shared_accounts: Use shared accounts for efficiency
            as_legacy_transaction: Return legacy transaction format
            compute_unit_price_micro_lamports: Priority fee in micro-lamports
            dynamic_compute_unit_limit: Use dynamic compute unit limit
            priority_level: Priority level ("low", "medium", "high")

        Returns:
            SwapTransaction with base64 encoded transaction

        Raises:
            JupiterError: If transaction building fails
        """
        payload = {
            "quoteResponse": {
                "inputMint": quote.input_mint,
                "inAmount": str(quote.input_amount),
                "outputMint": quote.output_mint,
                "outAmount": str(quote.output_amount),
                "otherAmountThreshold": str(quote.other_amount_threshold),
                "swapMode": quote.swap_mode,
                "slippageBps": quote.slippage_bps,
                "platformFee": quote.platform_fee,
                "priceImpactPct": str(quote.price_impact_pct / 100),  # Convert back to decimal
                "routePlan": quote.route_plan,
                "contextSlot": quote.context_slot,
                "timeTaken": quote.time_taken
            },
            "userPublicKey": user_public_key,
            "wrapAndUnwrapSol": wrap_and_unwrap_sol,
            "useSharedAccounts": use_shared_accounts,
            "asLegacyTransaction": as_legacy_transaction,
            "computeUnitPriceMicroLamports": compute_unit_price_micro_lamports,
            "dynamicComputeUnitLimit": dynamic_compute_unit_limit,
            "priorityLevel": priority_level
        }

        try:
            self.logger.debug(f"Building swap transaction for {user_public_key}")

            response = await self._client.post(
                JUPITER_SWAP_URL,
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()

            data = response.json()

            # Check for API errors
            if "error" in data:
                raise JupiterError(f"Jupiter swap API error: {data['error']}")

            if "swapTransaction" not in data:
                raise JupiterError("No swap transaction in response")

            swap_tx = SwapTransaction.from_response(data)

            self.logger.info("Swap transaction built successfully")
            return swap_tx

        except httpx.RequestError as e:
            self.logger.error(f"Jupiter swap request failed: {e}")
            raise JupiterError(f"Swap request failed: {e}")
        except httpx.HTTPStatusError as e:
            error_msg = f"Jupiter swap HTTP error: {e.response.status_code}"
            if e.response.status_code == 400:
                try:
                    error_data = e.response.json()
                    error_msg += f" - {error_data.get('error', 'Bad Request')}"
                except Exception:
                    pass
            self.logger.error(error_msg)
            raise JupiterError(error_msg)
        except Exception as e:
            self.logger.error(f"Jupiter swap processing error: {e}")
            raise JupiterError(f"Swap processing failed: {e}")

    def decode_transaction(self, swap_transaction: SwapTransaction) -> bytes:
        """
        Decode base64 transaction to bytes

        Args:
            swap_transaction: SwapTransaction object

        Returns:
            Raw transaction bytes

        Raises:
            JupiterError: If decoding fails
        """
        try:
            transaction_bytes = base64.b64decode(swap_transaction.swap_transaction)
            self.logger.debug("Transaction decoded successfully")
            return transaction_bytes
        except Exception as e:
            self.logger.error(f"Transaction decoding failed: {e}")
            raise JupiterError(f"Transaction decoding failed: {e}")

    async def get_swap_quote_and_build(self,
                                     user_public_key: str,
                                     input_mint: str,
                                     output_mint: str,
                                     amount: int,
                                     slippage_bps: int = 50,
                                     max_price_impact_pct: float = 1.0) -> tuple[JupiterQuote, Optional[SwapTransaction]]:
        """
        Convenience method to get quote and build transaction in one call

        Args:
            user_public_key: User's wallet public key
            input_mint: Input token mint address
            output_mint: Output token mint address
            amount: Input amount in smallest units
            slippage_bps: Slippage tolerance in basis points
            max_price_impact_pct: Maximum acceptable price impact

        Returns:
            Tuple of (quote, swap_transaction or None if impact too high)

        Raises:
            JupiterError: If quote or transaction building fails
        """
        # Get quote
        quote = await self.get_quote(
            input_mint=input_mint,
            output_mint=output_mint,
            amount=amount,
            slippage_bps=slippage_bps
        )

        # Check price impact
        if not quote.is_acceptable_impact(max_price_impact_pct):
            self.logger.warning(
                f"Price impact too high: {quote.price_impact_pct:.3f}% > {max_price_impact_pct}%"
            )
            return quote, None

        # Build transaction
        swap_tx = await self.build_swap_transaction(user_public_key, quote)
        return quote, swap_tx

# Utility functions

def lamports_to_sol(lamports: int) -> float:
    """Convert lamports to SOL"""
    return lamports / 1_000_000_000

def sol_to_lamports(sol: float) -> int:
    """Convert SOL to lamports"""
    return int(sol * 1_000_000_000)

def format_token_amount(amount: int, decimals: int) -> str:
    """Format token amount with proper decimal places"""
    decimal_amount = Decimal(amount) / Decimal(10 ** decimals)
    return str(decimal_amount.quantize(Decimal('0.000001'), rounding=ROUND_DOWN))

# Constants for common token mints
SOL_MINT = "So11111111111111111111111111111111111111112"
USDC_MINT = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
USDT_MINT = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"

if __name__ == "__main__":
    # Test Jupiter client functionality
    async def test_jupiter_client():
        print("🧪 Testing Jupiter client...")

        try:
            async with JupiterClient() as client:
                # Test quote request (SOL to USDC)
                quote = await client.get_quote(
                    input_mint=SOL_MINT,
                    output_mint=USDC_MINT,
                    amount=sol_to_lamports(0.01),  # 0.01 SOL
                    slippage_bps=50
                )

                print(f"✅ Quote received:")
                print(f"  Route: {quote.get_route_description()}")
                print(f"  Output: {format_token_amount(quote.output_amount, 6)} USDC")
                print(f"  Price impact: {quote.price_impact_pct:.3f}%")
                print(f"  Time taken: {quote.time_taken:.3f}s")

        except Exception as e:
            print(f"❌ Jupiter test failed: {e}")

    # Run the test
    asyncio.run(test_jupiter_client())
