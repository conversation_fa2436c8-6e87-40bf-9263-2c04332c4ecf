"""
Solana Memecoin Sniper Bot - Logging Utility

This module provides structured logging with consistent formatting,
performance tracking, and integration with the bot's configuration system.
"""
import logging
import sys
import time
from datetime import datetime, timezone
from typing import Optional, Dict, Any
from contextlib import contextmanager

# ANSI color codes for console output
class Colors:
    RESET = '[0m'
    BOLD = '[1m'
    RED = '[91m'
    GREEN = '[92m'
    YELLOW = '[93m'
    BLUE = '[94m'
    MAGENTA = '[95m'
    CYAN = '[96m'
    WHITE = '[97m'

class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging with colors and performance info"""

    COLORS = {
        'DEBUG': Colors.CYAN,
        'INFO': Colors.GREEN,
        'WARNING': Colors.YELLOW,
        'ERROR': Colors.RED,
        'CRITICAL': Colors.MAGENTA + Colors.BOLD
    }

    def format(self, record: logging.LogRecord) -> str:
        # Add timestamp in ISO format
        record.timestamp = datetime.fromtimestamp(record.created, tz=timezone.utc).isoformat()

        # Add color for console output
        color = self.COLORS.get(record.levelname, Colors.WHITE)
        record.colored_levelname = f"{color}{record.levelname}{Colors.RESET}"

        # Format the message
        formatted = super().format(record)

        return formatted

def setup_logger(name: str, level: str = "INFO", enable_colors: bool = True) -> logging.Logger:
    """
    Set up a structured logger with consistent formatting

    Args:
        name: Logger name (typically module name)
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        enable_colors: Whether to use colored output

    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)

    # Prevent duplicate handlers
    if logger.handlers:
        return logger

    logger.setLevel(getattr(logging, level.upper()))

    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, level.upper()))

    # Formatter
    if enable_colors:
        format_str = (
            "{colored_levelname} | "
            "{timestamp} | "
            "{name} | "
            "{funcName}:{lineno} | "
            "{message}"
        )
    else:
        format_str = (
            "{levelname} | "
            "{timestamp} | "
            "{name} | "
            "{funcName}:{lineno} | "
            "{message}"
        )

    formatter = StructuredFormatter(format_str, style='{')
    console_handler.setFormatter(formatter)

    logger.addHandler(console_handler)

    return logger

class PerformanceLogger:
    """Logger with built-in performance tracking capabilities"""

    def __init__(self, name: str, level: str = "INFO"):
        self.logger = setup_logger(name, level)
        self._timers: Dict[str, float] = {}

    def debug(self, msg: str, **kwargs):
        self.logger.debug(msg, **kwargs)

    def info(self, msg: str, **kwargs):
        self.logger.info(msg, **kwargs)

    def warning(self, msg: str, **kwargs):
        self.logger.warning(msg, **kwargs)

    def error(self, msg: str, **kwargs):
        self.logger.error(msg, **kwargs)

    def critical(self, msg: str, **kwargs):
        self.logger.critical(msg, **kwargs)

    def start_timer(self, name: str):
        """Start a named timer for performance tracking"""
        self._timers[name] = time.perf_counter()
        self.debug(f"Timer started: {name}")

    def stop_timer(self, name: str) -> float:
        """Stop a named timer and log the elapsed time"""
        if name not in self._timers:
            self.warning(f"Timer '{name}' was never started")
            return 0.0

        elapsed = time.perf_counter() - self._timers[name]
        del self._timers[name]

        self.info(f"Timer stopped: {name} took {elapsed:.3f}s")
        return elapsed

    @contextmanager
    def timer_context(self, name: str):
        """Context manager for automatic timing"""
        start_time = time.perf_counter()
        self.debug(f"Starting timed operation: {name}")

        try:
            yield
        finally:
            elapsed = time.perf_counter() - start_time
            self.info(f"Completed timed operation: {name} took {elapsed:.3f}s")

class TradingLogger(PerformanceLogger):
    """Specialized logger for trading events with structured data"""

    def __init__(self, name: str = "trading", level: str = "INFO"):
        super().__init__(name, level)

    def token_discovered(self, mint: str, source: str):
        """Log token discovery event"""
        self.info(f"Token discovered: {mint} from {source}")

    def rug_check_passed(self, mint: str, checks: Dict[str, bool]):
        """Log successful rug check"""
        passed_checks = [check for check, passed in checks.items() if passed]
        self.info(f"Rug check PASSED for {mint}: {', '.join(passed_checks)}")

    def rug_check_failed(self, mint: str, failed_checks: Dict[str, str]):
        """Log failed rug check with reasons"""
        failures = [f"{check}: {reason}" for check, reason in failed_checks.items()]
        self.warning(f"Rug check FAILED for {mint}: {'; '.join(failures)}")

    def quote_received(self, mint: str, input_amount: float, output_amount: float, 
                      price_impact: float, slippage_bps: int):
        """Log Jupiter quote information"""
        self.info(
            f"Quote received for {mint}: "
            f"{input_amount} SOL → {output_amount} tokens "
            f"(impact: {price_impact:.2f}%, slippage: {slippage_bps}bps)"
        )

    def trade_executed(self, mint: str, side: str, amount: float, price: float, 
                      tx_signature: str):
        """Log trade execution"""
        self.info(
            f"Trade executed: {side.upper()} {amount} of {mint} "
            f"at {price:.6f} SOL (tx: {tx_signature[:16]}...)"
        )

    def position_opened(self, mint: str, amount: float, entry_price: float, 
                       stop_loss: float, take_profit: float):
        """Log position opening"""
        self.info(
            f"Position opened: {amount} of {mint} at {entry_price:.6f} SOL "
            f"(SL: {stop_loss:.6f}, TP: {take_profit:.6f})"
        )

    def position_closed(self, mint: str, amount: float, entry_price: float, 
                       exit_price: float, pnl: float, reason: str):
        """Log position closing"""
        pnl_pct = (exit_price / entry_price - 1) * 100 if entry_price > 0 else 0
        status = "PROFIT" if pnl > 0 else "LOSS"

        self.info(
            f"Position closed: {amount} of {mint} "
            f"{entry_price:.6f} → {exit_price:.6f} SOL "
            f"({status}: {pnl_pct:+.2f}%, {reason})"
        )

    def circuit_breaker_triggered(self, reason: str, cooldown_minutes: int):
        """Log circuit breaker activation"""
        self.error(
            f"🚨 CIRCUIT BREAKER TRIGGERED: {reason}. "
            f"Trading halted for {cooldown_minutes} minutes."
        )

    def system_startup(self, config_summary: Dict[str, Any]):
        """Log system startup with configuration summary"""
        self.info("🚀 Solana Memecoin Sniper Bot starting up...")

        for key, value in config_summary.items():
            # Mask sensitive information
            if 'key' in key.lower() or 'secret' in key.lower():
                value = '*' * 8 if value else 'NOT_SET'
            self.info(f"Config {key}: {value}")

    def system_shutdown(self, reason: str):
        """Log system shutdown"""
        self.info(f"🛑 Solana Memecoin Sniper Bot shutting down: {reason}")

class SafetyLogger(PerformanceLogger):
    """Specialized logger for safety and risk management events"""

    def __init__(self, name: str = "safety", level: str = "INFO"):
        super().__init__(name, level)

    def authority_check(self, mint: str, mint_auth: Optional[str], freeze_auth: Optional[str]):
        """Log authority check results"""
        mint_status = "BURNED" if mint_auth is None else f"ACTIVE ({mint_auth[:8]}...)"
        freeze_status = "BURNED" if freeze_auth is None else f"ACTIVE ({freeze_auth[:8]}...)"

        self.info(f"Authority check for {mint}: Mint={mint_status}, Freeze={freeze_status}")

    def liquidity_check(self, mint: str, liquidity_sol: float, required_sol: float):
        """Log liquidity verification"""
        status = "PASS" if liquidity_sol >= required_sol else "FAIL"
        self.info(
            f"Liquidity check for {mint}: {liquidity_sol:.2f} SOL "
            f"(required: {required_sol:.2f}) - {status}"
        )

    def holder_concentration_check(self, mint: str, top10_pct: float, max_allowed: float):
        """Log holder concentration analysis"""
        status = "PASS" if top10_pct <= max_allowed else "FAIL"
        self.info(
            f"Holder concentration for {mint}: {top10_pct:.1f}% "
            f"(max: {max_allowed:.1f}%) - {status}"
        )

    def price_impact_check(self, mint: str, impact_pct: float, max_allowed: float):
        """Log price impact validation"""
        status = "PASS" if impact_pct <= max_allowed else "FAIL"
        self.info(
            f"Price impact for {mint}: {impact_pct:.2f}% "
            f"(max: {max_allowed:.2f}%) - {status}"
        )

# Global logger instances
main_logger = TradingLogger("main")
safety_logger = SafetyLogger("safety")
performance_logger = PerformanceLogger("performance")

def setup_logging(level: str = "INFO", log_file: Optional[str] = None) -> None:
    """Setup global logging configuration"""
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, level.upper()))
    
    # Clear existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Add console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, level.upper()))
    
    # Create formatter
    formatter = StructuredFormatter(
        "{levelname} | {timestamp} | {name} | {funcName}:{lineno} | {message}",
        enable_colors=True
    )
    console_handler.setFormatter(formatter)
    
    # Add file handler if specified
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(getattr(logging, level.upper()))
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    root_logger.addHandler(console_handler)

def get_logger(name: str = "bot") -> PerformanceLogger:
    """Get a logger instance for the specified module"""
    return PerformanceLogger(name)

if __name__ == "__main__":
    # Test the logging system
    logger = TradingLogger("test")

    logger.info("Testing logging system...")
    logger.token_discovered("So11111111111111111111111111111111111111112", "pump.fun")

    with logger.timer_context("test_operation"):
        import time
        time.sleep(0.1)  # Simulate some work

    logger.info("✅ Logging system test completed")
