"""
Solana Memecoin Sniper Bot - Pump.fun Watcher

This module is responsible for monitoring the Solana blockchain for new
token launches on the Pump.fun platform using WebSocket subscriptions.
"""
import asyncio
import json
from typing import AsyncGenerator, Dict, Any, Set, Optional
import websockets
from solders.pubkey import Pubkey
from solana.rpc.api import Client
from .config import get_config
from .logutil import get_logger

# Get configuration
config = get_config()
logger = get_logger(__name__)

# Pump.fun program ID and relevant instructions
PUMPFUN_PROGRAM_ID = Pubkey.from_string(config.api.pumpfun_program_id)
CREATE_INSTRUCTION_DISCRIMINATOR = "16921143342312133354" # create

from dataclasses import dataclass

@dataclass
class NewTokenEvent:
    """Represents a new token creation event on Pump.fun."""
    mint: str
    creator: str
    bonding_curve: str
    token_account: str
    timestamp: float

class PumpFunWatcher:
    """
    Watches for new token creation events on Pump.fun via WebSocket.
    """

    def __init__(self, ws_url: str, rpc_url: str):
        self.ws_url = ws_url
        self.rpc_client = Client(rpc_url)
        self._seen_signatures: Set[str] = set()
        logger.info(f"Pump.fun watcher initialized for WebSocket: {self.ws_url}")

    async def subscribe_to_pumpfun_logs(self) -> AsyncGenerator[NewTokenEvent, None]:
        """
        Subscribes to Pump.fun program logs and yields new token events.
        """
        while True:
            try:
                async with websockets.connect(self.ws_url) as websocket:
                    # Subscription request for logs of the Pump.fun program
                    subscription_request = {
                        "jsonrpc": "2.0",
                        "id": 1,
                        "method": "logsSubscribe",
                        "params": [
                            {"mentions": [str(PUMPFUN_PROGRAM_ID)]},
                            {"commitment": "confirmed"}
                        ]
                    }
                    await websocket.send(json.dumps(subscription_request))
                    
                    # Wait for the subscription confirmation
                    confirmation = await websocket.recv()
                    logger.info(f"Subscribed to Pump.fun logs: {confirmation}")

                    # Continuously listen for new log notifications
                    async for message in websocket:
                        event = self._parse_log_message(json.loads(message))
                        if event:
                            yield event
            
            except (websockets.exceptions.ConnectionClosedError, asyncio.TimeoutError) as e:
                logger.warning(f"WebSocket connection error: {e}. Reconnecting in 5 seconds...")
                await asyncio.sleep(5)
            except Exception as e:
                logger.error(f"An unexpected error occurred in Pump.fun watcher: {e}")
                await asyncio.sleep(10) # Wait longer for unexpected errors

    def _parse_log_message(self, message: Dict[str, Any]) -> Optional[NewTokenEvent]:
        """
        Parses a WebSocket log message to extract new token creation data.
        """
        if "params" not in message or "result" not in message["params"]:
            return None

        log_data = message["params"]["result"]["value"]
        logs = log_data.get("logs", [])
        signature = log_data.get("signature")

        if not signature or signature in self._seen_signatures:
            return None

        is_create_instruction = any(
            f"Instruction: Create" in log for log in logs
        )

        if is_create_instruction:
            try:
                # Fetch the full transaction details using the signature
                tx_response = self.rpc_client.get_transaction(signature, max_supported_transaction_version=0)
                if not tx_response or not tx_response.value:
                    return None

                transaction = tx_response.value.transaction
                accounts = transaction.message.account_keys
                
                # Extract account keys based on their positions in the 'create' instruction
                # This mapping is based on typical Pump.fun 'create' transaction structures.
                # Indices can be fragile; a more robust solution would parse instruction data.
                mint = str(accounts[0])
                bonding_curve = str(accounts[1])
                token_account = str(accounts[4])
                creator = str(accounts[6])

                self._seen_signatures.add(signature)
                logger.info(f"New Pump.fun token detected: {mint}")
                
                return NewTokenEvent(
                    mint=mint,
                    creator=creator,
                    bonding_curve=bonding_curve,
                    token_account=token_account,
                    timestamp=transaction.block_time
                )
            except Exception as e:
                logger.error(f"Error parsing transaction {signature}: {e}")
        
        return None

# Singleton instance
_watcher_instance: Optional[PumpFunWatcher] = None

def get_pumpfun_watcher() -> PumpFunWatcher:
    """
    Get the singleton instance of the PumpFunWatcher.
    """
    global _watcher_instance
    if _watcher_instance is None:
        _watcher_instance = PumpFunWatcher(
            ws_url=config.solana.sol_ws_url,
            rpc_url=config.solana.sol_rpc_url
        )
    return _watcher_instance

async def main():
    """Test function to watch for and print new token events."""
    logger.info("🧪 Starting Pump.fun watcher test...")
    watcher = get_pumpfun_watcher()
    
    print("Listening for new tokens on Pump.fun... (Press Ctrl+C to stop)")
    
    try:
        async for event in watcher.subscribe_to_pumpfun_logs():
            print("\n" + "="*40)
            print(f"🎉 New Token Discovered!")
            print(f"   - Mint: {event.mint}")
            print(f"   - Creator: {event.creator}")
            print(f"   - Bonding Curve: {event.bonding_curve}")
            print(f"   - Timestamp: {event.timestamp}")
            print("="*40 + "\n")
    except asyncio.CancelledError:
        logger.info("Watcher test stopped by user.")
    except Exception as e:
        logger.error(f"An error occurred during the test: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Shutdown requested.")
