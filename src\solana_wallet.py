"""
Production-Ready Solana Wallet Manager

Real wallet operations with keypair management, transaction signing, and balance tracking.
"""
import json
import base64
import struct
from typing import Optional, Dict, Any, List, Tuple
from dataclasses import dataclass
from nacl.signing import Signing<PERSON>ey
from nacl.encoding import RawEncoder
import base58
from .logutil import get_logger
from .rpc import SolanaRPCClient

@dataclass
class TransactionResult:
    """Transaction execution result"""
    success: bool
    signature: Optional[str] = None
    error: Optional[str] = None
    confirmation_time_ms: float = 0.0

class SolanaWallet:
    """Production-ready Solana wallet with real operations"""

    def __init__(self, secret_key_json: str, rpc_client: SolanaRPCClient):
        self.rpc_client = rpc_client
        self.logger = get_logger("wallet")

        # Parse secret key
        try:
            secret_key_array = json.loads(secret_key_json)
            if len(secret_key_array) != 64:
                raise ValueError("Secret key must be 64 bytes")

            # Create signing key from first 32 bytes
            signing_key_bytes = bytes(secret_key_array[:32])
            self.signing_key = SigningKey(signing_key_bytes)

            # Get public key
            public_key_bytes = self.signing_key.verify_key.encode()
            self.public_key = base58.b58encode(public_key_bytes).decode('utf-8')

            self.logger.info(f"Wallet initialized: {self.public_key}")

        except Exception as e:
            self.logger.error(f"Failed to initialize wallet: {e}")
            raise

    async def get_balance(self) -> float:
        """Get current SOL balance"""
        try:
            balance = await self.rpc_client.get_balance(self.public_key)
            return balance
        except Exception as e:
            self.logger.error(f"Failed to get balance: {e}")
            return 0.0

    async def get_token_balance(self, mint_address: str) -> float:
        """Get balance of specific token"""
        try:
            token_accounts = await self.rpc_client.get_token_accounts_by_owner(
                self.public_key, mint_address
            )

            total_balance = 0.0
            for account in token_accounts:
                account_info = account.get("account", {})
                parsed_info = account_info.get("data", {}).get("parsed", {})
                token_amount = parsed_info.get("info", {}).get("tokenAmount", {})

                if token_amount:
                    ui_amount = token_amount.get("uiAmount", 0.0)
                    if ui_amount:
                        total_balance += ui_amount

            return total_balance

        except Exception as e:
            self.logger.error(f"Failed to get token balance for {mint_address}: {e}")
            return 0.0

    async def get_all_token_balances(self) -> Dict[str, float]:
        """Get all token balances"""
        try:
            token_accounts = await self.rpc_client.get_token_accounts_by_owner(self.public_key)
            balances = {}

            for account in token_accounts:
                account_info = account.get("account", {})
                parsed_info = account_info.get("data", {}).get("parsed", {})
                token_info = parsed_info.get("info", {})

                mint = token_info.get("mint")
                token_amount = token_info.get("tokenAmount", {})
                ui_amount = token_amount.get("uiAmount", 0.0)

                if mint and ui_amount and ui_amount > 0:
                    balances[mint] = ui_amount

            return balances

        except Exception as e:
            self.logger.error(f"Failed to get token balances: {e}")
            return {}

    def sign_transaction(self, transaction_bytes: bytes) -> bytes:
        """Sign transaction with wallet's private key"""
        try:
            signature = self.signing_key.sign(transaction_bytes, encoder=RawEncoder)
            return signature.signature
        except Exception as e:
            self.logger.error(f"Failed to sign transaction: {e}")
            raise

    async def send_sol(self, to_address: str, amount_sol: float) -> TransactionResult:
        """Send SOL to another address"""
        try:
            # Create transfer instruction
            from solana.transaction import Transaction
            from solana.system_program import transfer, TransferParams
            from solana.publickey import PublicKey

            # Convert SOL to lamports
            lamports = int(amount_sol * 1_000_000_000)

            # Create transaction
            transaction = Transaction()
            transfer_instruction = transfer(
                TransferParams(
                    from_pubkey=PublicKey(self.public_key),
                    to_pubkey=PublicKey(to_address),
                    lamports=lamports
                )
            )
            transaction.add(transfer_instruction)

            # Get recent blockhash
            recent_blockhash = await self.rpc_client.get_recent_blockhash()
            if not recent_blockhash:
                return TransactionResult(success=False, error="Failed to get recent blockhash")

            transaction.recent_blockhash = recent_blockhash

            # Sign transaction
            transaction.sign(self.signing_key)

            # Send transaction
            signature = await self.rpc_client.send_transaction(
                base64.b64encode(transaction.serialize()).decode('utf-8')
            )

            if not signature:
                return TransactionResult(success=False, error="Failed to send transaction")

            # Confirm transaction
            confirmed = await self.rpc_client.confirm_transaction(signature)

            return TransactionResult(
                success=confirmed,
                signature=signature,
                error=None if confirmed else "Transaction failed to confirm"
            )

        except Exception as e:
            error_msg = f"Failed to send SOL: {str(e)}"
            self.logger.error(error_msg)
            return TransactionResult(success=False, error=error_msg)

    async def create_token_account(self, mint_address: str) -> Optional[str]:
        """Create associated token account for mint"""
        try:
            from solana.transaction import Transaction
            from solana.publickey import PublicKey
            from spl.token.instructions import create_associated_token_account

            # Calculate associated token account address
            mint_pubkey = PublicKey(mint_address)
            owner_pubkey = PublicKey(self.public_key)

            from spl.token.client import Token
            from solana.rpc.api import Client

            # Check if account already exists
            existing_accounts = await self.rpc_client.get_token_accounts_by_owner(
                self.public_key, mint_address
            )

            if existing_accounts:
                # Return existing account
                return existing_accounts[0]["pubkey"]

            # Create new associated token account
            transaction = Transaction()
            create_account_instruction = create_associated_token_account(
                payer=owner_pubkey,
                owner=owner_pubkey,
                mint=mint_pubkey
            )
            transaction.add(create_account_instruction)

            # Get recent blockhash
            recent_blockhash = await self.rpc_client.get_recent_blockhash()
            if not recent_blockhash:
                return None

            transaction.recent_blockhash = recent_blockhash

            # Sign and send
            transaction.sign(self.signing_key)
            signature = await self.rpc_client.send_transaction(
                base64.b64encode(transaction.serialize()).decode('utf-8')
            )

            if signature and await self.rpc_client.confirm_transaction(signature):
                # Calculate the associated token account address
                from spl.token.instructions import get_associated_token_address
                ata_address = get_associated_token_address(owner_pubkey, mint_pubkey)
                return str(ata_address)

            return None

        except Exception as e:
            self.logger.error(f"Failed to create token account: {e}")
            return None

def create_wallet_from_config(config) -> SolanaWallet:
    """Create wallet from configuration"""
    logger = get_logger("wallet_factory")

    try:
        # Import RPC client
        from .rpc import create_rpc_client
        import asyncio

        # Create RPC client
        loop = asyncio.get_event_loop()
        rpc_client = loop.run_until_complete(
            create_rpc_client(config.solana.sol_rpc_url, config.solana.sol_ws_url)
        )

        # Create wallet
        wallet = SolanaWallet(config.solana.sol_secret_key_json, rpc_client)

        logger.info(f"Wallet created successfully: {wallet.public_key}")
        return wallet

    except Exception as e:
        logger.error(f"Failed to create wallet: {e}")
        raise

if __name__ == "__main__":
    # Test wallet functionality
    import asyncio
    from .config import get_config
    from .rpc import create_rpc_client

    async def test_wallet():
        print("🧪 Testing Production Wallet...")

        try:
            # Load config
            config = get_config()

            # Create RPC client
            rpc_client = await create_rpc_client(config.solana.sol_rpc_url)

            # Create wallet
            wallet = SolanaWallet(config.solana.sol_secret_key_json, rpc_client)

            # Test balance
            balance = await wallet.get_balance()
            print(f"✅ SOL Balance: {balance}")

            # Test token balances
            token_balances = await wallet.get_all_token_balances()
            print(f"✅ Token Balances: {len(token_balances)} tokens")

            for mint, balance in list(token_balances.items())[:5]:  # Show first 5
                print(f"   {mint[:20]}...: {balance}")

            await rpc_client.close()
            print("✅ Wallet test completed")

        except Exception as e:
            print(f"❌ Wallet test failed: {e}")

    asyncio.run(test_wallet())
