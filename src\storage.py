"""
Solana Memecoin Sniper Bot - Data Persistence

This module provides SQLite-based data persistence for trade history,
performance metrics, and system telemetry with efficient querying.
"""
import sqlite3
import json
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import threading



@dataclass
class TokenRecord:
    """Token discovery and metadata record"""
    mint: str
    symbol: str
    name: str
    first_seen: datetime
    creator: str
    source: str  # 'pump.fun', 'jupiter', etc.
    metadata: Dict[str, Any]  # JSON metadata

    def to_dict(self) -> Dict[str, Any]:
        return {
            'mint': self.mint,
            'symbol': self.symbol,
            'name': self.name,
            'first_seen': self.first_seen.isoformat(),
            'creator': self.creator,
            'source': self.source,
            'metadata': json.dumps(self.metadata)
        }

@dataclass
class TradeRecord:
    """Individual trade execution record"""
    id: Optional[int] = None
    timestamp: Optional[datetime] = None
    mint: str = ""
    symbol: str = ""
    side: str = ""  # 'buy' or 'sell'
    amount_sol: float = 0.0
    amount_tokens: float = 0.0
    price_per_token: float = 0.0
    quoted_output: float = 0.0
    actual_output: float = 0.0
    slippage_bps: int = 0
    price_impact_pct: float = 0.0
    jupiter_route: str = ""
    tx_signature: str = ""
    gas_fee_sol: float = 0.0
    success: bool = True
    error_message: str = ""

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now(timezone.utc)

    def calculate_slippage_realized(self) -> float:
        """Calculate realized slippage vs quoted"""
        if self.quoted_output == 0:
            return 0.0
        return ((self.quoted_output - self.actual_output) / self.quoted_output) * 10000  # bps

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        if data['timestamp']:
            data['timestamp'] = data['timestamp'].isoformat()
        return data

@dataclass
class PositionRecord:
    """Open position tracking record"""
    id: Optional[int] = None
    mint: str = ""
    symbol: str = ""
    entry_timestamp: Optional[datetime] = None
    entry_price: float = 0.0
    quantity: float = 0.0
    stop_loss: float = 0.0
    take_profit: float = 0.0
    current_price: float = 0.0
    unrealized_pnl_sol: float = 0.0
    status: str = "open"  # 'open', 'closed', 'stopped'
    entry_trade_id: Optional[int] = None

    def __post_init__(self):
        if self.entry_timestamp is None:
            self.entry_timestamp = datetime.now(timezone.utc)

    def update_unrealized_pnl(self, current_price: float):
        """Update current price and unrealized PnL"""
        self.current_price = current_price
        if self.entry_price > 0:
            self.unrealized_pnl_sol = (current_price - self.entry_price) * self.quantity

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        if data['entry_timestamp']:
            data['entry_timestamp'] = data['entry_timestamp'].isoformat()
        return data

@dataclass
class PerformanceSnapshot:
    """Performance metrics snapshot"""
    timestamp: datetime
    total_trades: int
    winning_trades: int
    total_pnl_sol: float
    win_rate_pct: float
    avg_win_sol: float
    avg_loss_sol: float
    max_drawdown_pct: float
    sharpe_ratio: float
    total_fees_sol: float

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['timestamp'] = data['timestamp'].isoformat()
        return data

class TradingDatabase:
    """
    SQLite database manager for trading data

    Provides thread-safe database operations with optimized schemas
    for high-frequency trading data storage and retrieval.
    """

    def __init__(self, db_path: str = "trading_bot.db"):
        """
        Initialize database connection and create tables

        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = Path(db_path)
        self._local = threading.local()

        # Initialize logger
        from logutil import get_logger
        self.logger = get_logger("database")

        # Create tables
        self._create_tables()

        self.logger.info(f"Database initialized: {self.db_path}")

    def _get_connection(self) -> sqlite3.Connection:
        """Get thread-local database connection"""
        if not hasattr(self._local, 'connection'):
            self._local.connection = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0
            )
            # Enable WAL mode for better concurrency
            self._local.connection.execute("PRAGMA journal_mode=WAL")
            # Enable foreign keys
            self._local.connection.execute("PRAGMA foreign_keys=ON")

        return self._local.connection

    def _create_tables(self):
        """Create database tables if they don't exist"""
        conn = self._get_connection()

        # Tokens table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS tokens (
                mint TEXT PRIMARY KEY,
                symbol TEXT NOT NULL,
                name TEXT NOT NULL,
                first_seen TEXT NOT NULL,
                creator TEXT NOT NULL,
                source TEXT NOT NULL,
                metadata TEXT NOT NULL,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Trades table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                mint TEXT NOT NULL,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL CHECK (side IN ('buy', 'sell')),
                amount_sol REAL NOT NULL,
                amount_tokens REAL NOT NULL,
                price_per_token REAL NOT NULL,
                quoted_output REAL NOT NULL,
                actual_output REAL NOT NULL,
                slippage_bps INTEGER NOT NULL,
                price_impact_pct REAL NOT NULL,
                jupiter_route TEXT NOT NULL,
                tx_signature TEXT NOT NULL,
                gas_fee_sol REAL NOT NULL DEFAULT 0,
                success BOOLEAN NOT NULL DEFAULT 1,
                error_message TEXT DEFAULT '',
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (mint) REFERENCES tokens (mint)
            )
        """)

        # Positions table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                mint TEXT NOT NULL,
                symbol TEXT NOT NULL,
                entry_timestamp TEXT NOT NULL,
                entry_price REAL NOT NULL,
                quantity REAL NOT NULL,
                stop_loss REAL NOT NULL,
                take_profit REAL NOT NULL,
                current_price REAL NOT NULL DEFAULT 0,
                unrealized_pnl_sol REAL NOT NULL DEFAULT 0,
                status TEXT NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'closed', 'stopped')),
                entry_trade_id INTEGER,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (mint) REFERENCES tokens (mint),
                FOREIGN KEY (entry_trade_id) REFERENCES trades (id)
            )
        """)

        # Performance snapshots table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS performance_snapshots (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                total_trades INTEGER NOT NULL,
                winning_trades INTEGER NOT NULL,
                total_pnl_sol REAL NOT NULL,
                win_rate_pct REAL NOT NULL,
                avg_win_sol REAL NOT NULL,
                avg_loss_sol REAL NOT NULL,
                max_drawdown_pct REAL NOT NULL,
                sharpe_ratio REAL NOT NULL,
                total_fees_sol REAL NOT NULL,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Create indexes for performance
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades (timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_trades_mint ON trades (mint)",
            "CREATE INDEX IF NOT EXISTS idx_trades_side ON trades (side)",
            "CREATE INDEX IF NOT EXISTS idx_positions_mint ON positions (mint)",
            "CREATE INDEX IF NOT EXISTS idx_positions_status ON positions (status)",
            "CREATE INDEX IF NOT EXISTS idx_performance_timestamp ON performance_snapshots (timestamp)"
        ]

        for index_sql in indexes:
            conn.execute(index_sql)

        conn.commit()
        self.logger.debug("Database tables created/verified")

    def insert_token(self, token: TokenRecord) -> bool:
        """
        Insert or update token record

        Args:
            token: TokenRecord to insert

        Returns:
            True if successful, False otherwise
        """
        try:
            conn = self._get_connection()

            conn.execute("""
                INSERT OR REPLACE INTO tokens 
                (mint, symbol, name, first_seen, creator, source, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                token.mint,
                token.symbol,
                token.name, 
                token.first_seen.isoformat(),
                token.creator,
                token.source,
                json.dumps(token.metadata)
            ))

            conn.commit()
            self.logger.debug(f"Token inserted: {token.symbol} ({token.mint})")
            return True

        except Exception as e:
            self.logger.error(f"Failed to insert token {token.mint}: {e}")
            return False

    def insert_trade(self, trade: TradeRecord) -> Optional[int]:
        """
        Insert trade record

        Args:
            trade: TradeRecord to insert

        Returns:
            Trade ID if successful, None otherwise
        """
        try:
            conn = self._get_connection()

            cursor = conn.execute("""
                INSERT INTO trades (
                    timestamp, mint, symbol, side, amount_sol, amount_tokens,
                    price_per_token, quoted_output, actual_output, slippage_bps,
                    price_impact_pct, jupiter_route, tx_signature, gas_fee_sol,
                    success, error_message
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                trade.timestamp.isoformat(),
                trade.mint,
                trade.symbol,
                trade.side,
                trade.amount_sol,
                trade.amount_tokens,
                trade.price_per_token,
                trade.quoted_output,
                trade.actual_output,
                trade.slippage_bps,
                trade.price_impact_pct,
                trade.jupiter_route,
                trade.tx_signature,
                trade.gas_fee_sol,
                trade.success,
                trade.error_message
            ))

            conn.commit()
            trade_id = cursor.lastrowid

            self.logger.info(
                f"Trade inserted: {trade.side} {trade.symbol} "
                f"({trade.amount_sol:.4f} SOL) - ID: {trade_id}"
            )

            return trade_id

        except Exception as e:
            self.logger.error(f"Failed to insert trade: {e}")
            return None

    def insert_position(self, position: PositionRecord) -> Optional[int]:
        """
        Insert position record

        Args:
            position: PositionRecord to insert

        Returns:
            Position ID if successful, None otherwise
        """
        try:
            conn = self._get_connection()

            cursor = conn.execute("""
                INSERT INTO positions (
                    mint, symbol, entry_timestamp, entry_price, quantity,
                    stop_loss, take_profit, current_price, unrealized_pnl_sol,
                    status, entry_trade_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                position.mint,
                position.symbol,
                position.entry_timestamp.isoformat(),
                position.entry_price,
                position.quantity,
                position.stop_loss,
                position.take_profit,
                position.current_price,
                position.unrealized_pnl_sol,
                position.status,
                position.entry_trade_id
            ))

            conn.commit()
            position_id = cursor.lastrowid

            self.logger.info(f"Position inserted: {position.symbol} - ID: {position_id}")
            return position_id

        except Exception as e:
            self.logger.error(f"Failed to insert position: {e}")
            return None

    def update_position(self, position_id: int, **updates) -> bool:
        """
        Update position record

        Args:
            position_id: Position ID to update
            **updates: Field updates

        Returns:
            True if successful, False otherwise
        """
        if not updates:
            return False

        try:
            conn = self._get_connection()

            # Build update query
            set_clause = ", ".join(f"{key} = ?" for key in updates.keys())
            values = list(updates.values()) + [position_id]

            # Add updated timestamp
            set_clause += ", updated_at = CURRENT_TIMESTAMP"

            conn.execute(f"""
                UPDATE positions SET {set_clause} WHERE id = ?
            """, values)

            conn.commit()

            if conn.total_changes > 0:
                self.logger.debug(f"Position {position_id} updated")
                return True
            else:
                self.logger.warning(f"Position {position_id} not found for update")
                return False

        except Exception as e:
            self.logger.error(f"Failed to update position {position_id}: {e}")
            return False

    def get_open_positions(self) -> List[PositionRecord]:
        """
        Get all open positions

        Returns:
            List of open PositionRecord objects
        """
        try:
            conn = self._get_connection()

            cursor = conn.execute("""
                SELECT * FROM positions WHERE status = 'open' ORDER BY entry_timestamp DESC
            """)

            positions = []
            for row in cursor.fetchall():
                position = PositionRecord(
                    id=row[0],
                    mint=row[1],
                    symbol=row[2],
                    entry_timestamp=datetime.fromisoformat(row[3]),
                    entry_price=row[4],
                    quantity=row[5],
                    stop_loss=row[6],
                    take_profit=row[7],
                    current_price=row[8],
                    unrealized_pnl_sol=row[9],
                    status=row[10],
                    entry_trade_id=row[11]
                )
                positions.append(position)

            return positions

        except Exception as e:
            self.logger.error(f"Failed to get open positions: {e}")
            return []

    def get_recent_trades(self, limit: int = 100) -> List[TradeRecord]:
        """
        Get recent trades

        Args:
            limit: Maximum number of trades to return

        Returns:
            List of recent TradeRecord objects
        """
        try:
            conn = self._get_connection()

            cursor = conn.execute("""
                SELECT * FROM trades ORDER BY timestamp DESC LIMIT ?
            """, (limit,))

            trades = []
            for row in cursor.fetchall():
                trade = TradeRecord(
                    id=row[0],
                    timestamp=datetime.fromisoformat(row[1]),
                    mint=row[2],
                    symbol=row[3],
                    side=row[4],
                    amount_sol=row[5],
                    amount_tokens=row[6],
                    price_per_token=row[7],
                    quoted_output=row[8],
                    actual_output=row[9],
                    slippage_bps=row[10],
                    price_impact_pct=row[11],
                    jupiter_route=row[12],
                    tx_signature=row[13],
                    gas_fee_sol=row[14],
                    success=bool(row[15]),
                    error_message=row[16] or ""
                )
                trades.append(trade)

            return trades

        except Exception as e:
            self.logger.error(f"Failed to get recent trades: {e}")
            return []

    def calculate_performance_metrics(self, days: int = 30) -> Optional[PerformanceSnapshot]:
        """
        Calculate performance metrics for the specified period

        Args:
            days: Number of days to analyze

        Returns:
            PerformanceSnapshot or None if calculation fails
        """
        try:
            conn = self._get_connection()

            # Get trades from the last N days
            cursor = conn.execute("""
                SELECT side, amount_sol, gas_fee_sol, success, timestamp
                FROM trades 
                WHERE timestamp > datetime('now', '-{} days')
                AND success = 1
                ORDER BY timestamp
            """.replace('{}', str(days)))

            trades = cursor.fetchall()

            if not trades:
                return PerformanceSnapshot(
                    timestamp=datetime.now(timezone.utc),
                    total_trades=0,
                    winning_trades=0,
                    total_pnl_sol=0.0,
                    win_rate_pct=0.0,
                    avg_win_sol=0.0,
                    avg_loss_sol=0.0,
                    max_drawdown_pct=0.0,
                    sharpe_ratio=0.0,
                    total_fees_sol=0.0
                )

            # Calculate basic metrics
            total_trades = len(trades)
            total_fees = sum(trade[2] for trade in trades)  # gas_fee_sol

            # Calculate PnL (simplified - would need more complex logic for actual PnL)
            # For now, assume equal buy/sell amounts indicate closed positions
            total_pnl = 0.0
            winning_trades = 0
            wins = []
            losses = []

            # This is a simplified calculation - in reality, you'd match buys with sells
            buy_volume = sum(trade[1] for trade in trades if trade[0] == 'buy')
            sell_volume = sum(trade[1] for trade in trades if trade[0] == 'sell')

            # Rough estimate of performance
            if buy_volume > 0:
                performance_ratio = sell_volume / buy_volume
                if performance_ratio > 1:
                    winning_trades = total_trades // 2  # Rough estimate
                    total_pnl = sell_volume - buy_volume - total_fees

            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
            avg_win = total_pnl / winning_trades if winning_trades > 0 else 0
            avg_loss = 0  # Would need more complex calculation

            return PerformanceSnapshot(
                timestamp=datetime.now(timezone.utc),
                total_trades=total_trades,
                winning_trades=winning_trades,
                total_pnl_sol=total_pnl,
                win_rate_pct=win_rate,
                avg_win_sol=avg_win,
                avg_loss_sol=avg_loss,
                max_drawdown_pct=0.0,  # Would need equity curve calculation
                sharpe_ratio=0.0,      # Would need returns analysis
                total_fees_sol=total_fees
            )

        except Exception as e:
            self.logger.error(f"Failed to calculate performance metrics: {e}")
            return None

    def insert_performance_snapshot(self, snapshot: PerformanceSnapshot) -> bool:
        """
        Insert performance snapshot

        Args:
            snapshot: PerformanceSnapshot to insert

        Returns:
            True if successful, False otherwise
        """
        try:
            conn = self._get_connection()

            conn.execute("""
                INSERT INTO performance_snapshots (
                    timestamp, total_trades, winning_trades, total_pnl_sol,
                    win_rate_pct, avg_win_sol, avg_loss_sol, max_drawdown_pct,
                    sharpe_ratio, total_fees_sol
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                snapshot.timestamp.isoformat(),
                snapshot.total_trades,
                snapshot.winning_trades,
                snapshot.total_pnl_sol,
                snapshot.win_rate_pct,
                snapshot.avg_win_sol,
                snapshot.avg_loss_sol,
                snapshot.max_drawdown_pct,
                snapshot.sharpe_ratio,
                snapshot.total_fees_sol
            ))

            conn.commit()
            self.logger.debug("Performance snapshot inserted")
            return True

        except Exception as e:
            self.logger.error(f"Failed to insert performance snapshot: {e}")
            return False

    def close(self):
        """Close database connection"""
        if hasattr(self._local, 'connection'):
            self._local.connection.close()
            delattr(self._local, 'connection')

        self.logger.debug("Database connection closed")

# Global database instance
_db_instance = None
_db_lock = threading.Lock()

def get_database(db_path: str = "trading_bot.db") -> TradingDatabase:
    """Get global database instance (singleton)"""
    global _db_instance

    with _db_lock:
        if _db_instance is None:
            _db_instance = TradingDatabase(db_path)
        return _db_instance

if __name__ == "__main__":
    # Test database functionality
    print("🧪 Testing database functionality...")

    try:
        db = TradingDatabase(":memory:")  # In-memory for testing

        # Test token insertion
        token = TokenRecord(
            mint="So11111111111111111111111111111111111111112",
            symbol="SOL",
            name="Solana",
            first_seen=datetime.now(timezone.utc),
            creator="11111111111111111111111111111111",
            source="system",
            metadata={"decimals": 9}
        )

        success = db.insert_token(token)
        print(f"✅ Token insertion: {'Success' if success else 'Failed'}")

        # Test trade insertion
        trade = TradeRecord(
            mint="So11111111111111111111111111111111111111112",
            symbol="SOL",
            side="buy",
            amount_sol=0.01,
            amount_tokens=100.0,
            price_per_token=0.0001,
            quoted_output=100.0,
            actual_output=99.5,
            slippage_bps=50,
            price_impact_pct=0.1,
            jupiter_route="Direct",
            tx_signature="test_signature_123",
            success=True
        )

        trade_id = db.insert_trade(trade)
        print(f"✅ Trade insertion: {'Success' if trade_id else 'Failed'}")

        # Test performance calculation
        perf = db.calculate_performance_metrics(30)
        print(f"✅ Performance calculation: {'Success' if perf else 'Failed'}")

        if perf:
            print(f"  Total trades: {perf.total_trades}")
            print(f"  Total PnL: {perf.total_pnl_sol:.6f} SOL")

        db.close()

    except Exception as e:
        print(f"❌ Database test failed: {e}")
